// { [key: string]: any }[]
// export const routersListb: Menu.MenuOptions[] = [
//   {
//     path: "/home",
//     name: "home",
//     component: "/home/<USER>",
//     meta: {
//       icon: "HomeFilled",
//       title: "首页",
//       isKeepAlive: true,
//       isHide: false,
//       isLink: "",
//       isFull: false,
//       isAffix: false
//     }
//   },
//   {
//     path: "/tests",
//     name: "tests",
//     component: "/tests/index",
//     meta: {
//       icon: "Shop",
//       title: "测试",
//       isKeepAlive: true
//     }
//   },
//   {
//     path: "/shop",
//     name: "shop",
//     component: "/shop",
//     meta: {
//       icon: "Shop",
//       title: "店铺",
//       isKeepAlive: true
//     },
//     children: [
//       {
//         path: "/shop/list",
//         name: "shopList",
//         component: "/shop/list/index",
//         meta: {
//           title: "店铺列表",
//           isKeepAlive: true
//         }
//       },
//       {
//         path: "/shop/cost",
//         name: "shopCost",
//         component: "/shop/cost/index",
//         meta: {
//           title: "店铺成本",
//           isKeepAlive: true
//         }
//       }
//     ]
//   },
//   {
//     path: "/product",
//     name: "product",
//     component: "/product",
//     meta: {
//       icon: "GoodsFilled",
//       title: "商品",
//       isKeepAlive: true
//     },
//     children: [
//       {
//         path: "/product/statistic",
//         name: "productStatistic",
//         component: "/product/statistic/index",
//         meta: {
//           title: "商品统计",
//           isKeepAlive: true
//         }
//       },
//       {
//         path: "/product/collection",
//         name: "productCollection",
//         component: "/product/collection/index",
//         meta: {
//           title: "商品采集",
//           isKeepAlive: true
//         }
//       },
//       {
//         path: "/product/source",
//         name: "productSource",
//         component: "/product/source/index",
//         meta: {
//           title: "商品货源",
//           isKeepAlive: true
//         }
//       },
//       {
//         path: "/product/publish",
//         name: "productPublish",
//         component: "/product/publish/index",
//         meta: {
//           title: "商品发布",
//           isKeepAlive: true
//         }
//       },
//       {
//         path: "/product/list",
//         name: "productList",
//         component: "/product/list/index",
//         meta: {
//           title: "商品列表",
//           isKeepAlive: true
//         }
//       }
//     ]
//   },
//   {
//     path: "/order",
//     name: "order",
//     component: "/order",
//     meta: {
//       icon: "List",
//       title: "订单",
//       isKeepAlive: true
//     },
//     children: [
//       {
//         path: "/order/statistic",
//         name: "orderStatistic",
//         component: "/order/statistic/index",
//         meta: {
//           title: "订单统计",
//           isKeepAlive: true
//         }
//       },
//       {
//         path: "/order/list",
//         name: "orderList",
//         component: "/order/list/index",
//         meta: {
//           title: "订单列表",
//           isKeepAlive: true
//         }
//       },
//       {
//         path: "/order/refund",
//         name: "orderRefund",
//         component: "/order/refund/index",
//         meta: {
//           title: "售后订单",
//           isKeepAlive: true
//         }
//       }
//     ]
//   },
//   {
//     path: "/service",
//     name: "service",
//     component: "/service",
//     meta: {
//       icon: "UserFilled",
//       title: "客服",
//       isKeepAlive: true
//     },
//     children: [
//       {
//         path: "/service/statistic",
//         name: "serviceStatistic",
//         component: "/service/statistic/index",
//         meta: {
//           title: "客服统计",
//           isKeepAlive: true
//         }
//       }
//     ]
//   },
//   {
//     path: "/script",
//     name: "script",
//     redirect: "/script",
//     meta: {
//       icon: "CirclePlusFilled",
//       title: "脚本",
//       isKeepAlive: true
//     },
//     children: [
//       {
//         path: "/script/deviceDebug",
//         name: "deviceDebug",
//         component: "/script/deviceDebug/index",
//         meta: {
//           title: "控制台",
//           isKeepAlive: true
//         }
//       },
//       {
//         path: "/script/device",
//         name: "scriptDevice",
//         component: "/script/device/index",
//         meta: {
//           title: "设备列表",
//           isKeepAlive: true
//         }
//       },
//       // {
//       //   path: "/script/device/add",
//       //   name: "scriptDeviceAdd",
//       //   component: "/script/device/add",
//       //   meta: {
//       //     title: "新增设备",
//       //     isKeepAlive: true
//       //   }
//       // },
//       {
//         path: "/script/account",
//         name: "scriptAccount",
//         component: "/script/account/index",
//         meta: {
//           title: "账号列表",
//           isKeepAlive: true
//         }
//       },
//       // {
//       //   path: "/script/setup",
//       //   name: "scriptSetup",
//       //   component: "/script/setup/index",
//       //   meta: {
//       //     title: "脚本配置",
//       //     isKeepAlive: true
//       //   }
//       // },
//       {
//         path: "/script/tasks/sc",
//         name: "scriptTasksSc",
//         component: "/script/tasks/sc/index",
//         meta: {
//           title: "收菜设置",
//           isKeepAlive: true
//         }
//       },
//       {
//         path: "/script/tasks/order",
//         name: "scriptTasksOrder",
//         component: "/script/tasks/order/index",
//         meta: {
//           title: "下单任务",
//           isKeepAlive: true
//         }
//       },
//       {
//         path: "/script/tasks/order-list",
//         name: "scriptOrder",
//         component: "/script/order/index",
//         meta: {
//           title: "订单列表",
//           isKeepAlive: true
//         }
//       }
//       // {
//       //   path: "/script/profile",
//       //   name: "scriptProfile",
//       //   component: "/script/profile/index",
//       //   meta: {
//       //     title: "脚本资料",
//       //     isLink: "",
//       //     isHide: false,
//       //     isFull: false,
//       //     isAffix: false,
//       //     isKeepAlive: true
//       //   }
//       // }
//     ]
//   },
//   {
//     path: "/user",
//     name: "user",
//     component: "/user/index",
//     meta: {
//       icon: "BellFilled",
//       title: "提醒",
//       isKeepAlive: true
//     }
//   },
//   {
//     path: "/system/settings",
//     name: "system",
//     component: "/system/settings/index",
//     meta: {
//       icon: "Tools",
//       title: "设置",
//       isKeepAlive: true
//     }
//     // children: [
//     //   {
//     //     path: "/system/settings",
//     //     name: "systemSettings",
//     //     component: "/system/settings/index",
//     //     meta: {
//     //       title: "系统设置",
//     //       isKeepAlive: true
//     //     }
//     //   }
//     // ]
//   }
// ];

export const routersList: Menu.MenuOptions[] = [
  {
    path: "/home",
    name: "home",
    component: "/home/<USER>",
    meta: {
      icon: "HomeFilled",
      title: "首页",
      isKeepAlive: true,
      isHide: false,
      isLink: "",
      isFull: false,
      isAffix: false
    }
  },
  {
    path: "/shop",
    name: "shop",
    component: "/shop",
    meta: {
      icon: "Shop",
      title: "店铺",
      isKeepAlive: true,
      isHide: false,
      isFull: false,
      isAffix: false
    },
    children: [
      {
        path: "/shop/list",
        name: "shopList",
        component: "/shop/list/index",
        meta: {
          icon: "Shop",
          title: "店铺列表",
          isKeepAlive: true,
          isHide: false,
          isFull: false,
          isAffix: false
        }
      },
      {
        path: "/shop/cost",
        name: "shopCost",
        component: "/shop/cost/index",
        meta: {
          icon: "Shop",
          title: "店铺成本",
          isKeepAlive: true,
          isHide: false,
          isFull: false,
          isAffix: false
        }
      }
    ]
  },
  {
    path: "/product",
    name: "product",
    component: "/product",
    meta: {
      icon: "GoodsFilled",
      title: "商品",
      isKeepAlive: true,
      isHide: false,
      isFull: false,
      isAffix: false
    },
    children: [
      {
        path: "/product/statistic",
        name: "productStatistic",
        component: "/product/statistic/index",
        meta: {
          title: "商品统计",
          isKeepAlive: true,
          icon: "Shop",
          isHide: false,
          isFull: false,
          isAffix: false
        }
      },
      {
        path: "/product/collection",
        name: "productCollection",
        component: "/product/collection/index",
        meta: {
          title: "商品采集",
          isKeepAlive: true,
          icon: "Shop",
          isHide: false,
          isFull: false,
          isAffix: false
        }
      },
      {
        path: "/product/source",
        name: "productSource",
        component: "/product/source/index",
        meta: {
          title: "货源列表",
          isKeepAlive: true,
          icon: "Shop",
          isHide: false,
          isFull: false,
          isAffix: false
        }
      },
      {
        path: "/product/publish",
        name: "productPublish",
        component: "/product/publish/index",
        meta: {
          title: "商品发布",
          isKeepAlive: true,
          icon: "Shop",
          isHide: false,
          isFull: false,
          isAffix: false
        }
      },
      {
        path: "/product/list",
        name: "productList",
        component: "/product/list/index",
        meta: {
          title: "商品列表",
          isKeepAlive: true,
          icon: "Shop",
          isHide: false,
          isFull: false,
          isAffix: false
        }
      }
    ]
  }
];
