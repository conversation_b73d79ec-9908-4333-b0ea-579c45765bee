// ? Element 常用表单校验规则

/**
 *  @rule 手机号
 */
export function checkPhoneNumber(rule: any, value: any, callback: any) {
  const regexp = /^(((13[0-9]{1})|(15[0-9]{1})|(16[0-9]{1})|(17[3-8]{1})|(18[0-9]{1})|(19[0-9]{1})|(14[5-7]{1}))+\d{8})$/;
  if (value === "") callback("请输入手机号码");
  if (!regexp.test(value)) {
    callback(new Error("请输入正确的手机号码"));
  } else {
    return callback();
  }
}

/**
 * @rule 用户名
 */
//  要求是: 只能使用大小写字母开头, 可以使用数字, 下划线, 长度为 6-16 位
export function checkUsername(rule: any, value: any, callback: any) {
  const regexp = /^[a-zA-Z][a-zA-Z0-9_]{5,15}$/;
  if (value === "") callback("请输入用户名");
  if (!regexp.test(value)) {
    callback(new Error("只能使用大小写字母开头, 可以使用数字, 下划线, 长度为 6-16 位"));
  } else {
    return callback();
  }
}

// /**
//  * @rule 密码
//  * 要求是: 只能使用大小写字母开头, 可以使用数字, 下划线, 长度为 6-16 位
//  */
export function checkPassword(rule: any, value: any, callback: any) {
  const regexp = /^[a-zA-Z0-9]{6,16}$/;
  if (value === "") callback("请输入密码");
  if (!regexp.test(value)) {
    callback(new Error("密码格式错误"));
  } else {
    return callback();
  }
}
/**
 * @rule 邮箱
 * 要求只能是@qq.com
 */
export function checkEmail(rule: any, value: any, callback: any) {
  const regexp = /^.+@qq\.com$/;
  if (value === "") {
    callback("请输入邮箱");
  } else if (!regexp.test(value)) {
    callback(new Error("仅支持QQ邮箱"));
  } else {
    callback();
  }
}

export const validateIpAddress = (rule: any, value: string, callback: any) => {
  if (value === "" || typeof value === "undefined" || value == null) {
    callback(new Error("请输入正确的IP地址"));
  } else {
    const reg =
      /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
    if (!reg.test(value) && value !== "") {
      callback(new Error("请输入正确的IP地址"));
    } else {
      callback();
    }
  }
};
