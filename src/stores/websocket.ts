import { defineStore } from "pinia";
import { ref } from "vue";

export const useWebSocketStore = defineStore("websocket", () => {
  const ws = ref<WebSocket | null>(null);
  const devices = ref<string[]>([]);
  const isConnected = ref(false);

  const connectWebSocket = () => {
    ws.value = new WebSocket("ws://127.0.0.1:8766");

    ws.value.onopen = () => {
      console.log("WebSocket连接已建立");
      isConnected.value = true;
      fetchDevices();
    };

    ws.value.onmessage = event => {
      const data = JSON.parse(event.data);
      if (data.status === "success" && data.connections) {
        devices.value = data.connections;
      }
    };

    ws.value.onclose = () => {
      console.log("WebSocket连接已关闭");
      isConnected.value = false;
      // 尝试重新连接
      setTimeout(connectWebSocket, 3000);
    };

    ws.value.onerror = error => {
      console.error("WebSocket错误:", error);
      isConnected.value = false;
    };
  };

  const fetchDevices = () => {
    if (ws.value && ws.value.readyState === WebSocket.OPEN) {
      ws.value.send(JSON.stringify({ command: "list_connections" }));
    }
  };

  const sendMessageToDevice = (serialNumber: string, command: string) => {
    if (ws.value && ws.value.readyState === WebSocket.OPEN) {
      ws.value.send(
        JSON.stringify({
          type: "command",
          device: serialNumber,
          command: command
        })
      );
    }
  };

  return {
    devices,
    isConnected,
    connectWebSocket,
    fetchDevices,
    sendMessageToDevice
  };
});
