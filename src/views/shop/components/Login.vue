<template>
  <el-dialog
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :destroy-on-close="true"
    v-model="dialogVisible"
    title="添加店铺"
    width="20%"
    :align-center="true"
    @close="handleClose"
    class="card"
  >
    <div style="width: 100%; text-align: center">
      <img width="150" :src="dialogProps.row.row.image" />
      <div v-if="scanMsg === '手机客户端确认登录'" style="margin-top: 30px; font-size: 12px; color: red">{{ scanMsg }}</div>
      <div v-else class="mt10 scan-tips">请使用手机京麦App扫码</div>
    </div>
  </el-dialog>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import { getLoginStatus } from "@/views/shop/index";
import { ElMessage } from "element-plus";
const scanMsg = ref("");
const dialogVisible = ref(false);

interface Props {
  row: { [key: string]: any };
}
const dialogProps = ref<Props>({
  row: {}
});
// 定义定时器 ID
let intervalId: NodeJS.Timeout;

const acceptParams = (row: Props) => {
  dialogProps.value.row = row;
  dialogVisible.value = true;
  intervalId = setInterval(() => {
    getLoginStatus({ key: row.row.key }).then(res => {
      scanMsg.value = res.data.msg;
      if (res.data.status) {
        ElMessage.success("操作成功");
        dialogProps.value.row.row.getTableList!();
        handleClose();
      } else {
        if (res.data.qrCodeStateType === "CANCELAUTH") {
          ElMessage.error("二维码已取消授权");
          handleClose();
        }
      }
    });
  }, 2000);
};

defineExpose({
  acceptParams
});

const handleClose = () => {
  // 停止定时器
  clearInterval(intervalId);
  scanMsg.value = "";
  dialogVisible.value = false;
};
</script>
<style scoped>
.scan-tips {
  font-size: 12px;
}
</style>
