<template>
  <div class="table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getShopList" highlight-current-row :pagination="false">
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button class="button" type="primary" @click="handelShopLogin">添加店铺</el-button>
        <el-button type="primary" @click="openAppDialog">自研应用</el-button>
        <el-button type="primary" @click="openDialogTasks('监控店铺Cookie状态', 'shop.cookie')">监控Cookie状态</el-button>
      </template>
      <!-- 表格操作 -->
      <template #operation>
        <el-button type="primary" plain @click="handelShopLogin">刷新</el-button>
        <!-- <el-button type="primary" @click="handleAddShop(scope.row)">编辑</el-button> -->
      </template>
    </ProTable>
    <Login ref="loginRef" />
    <!-- 自研应用 -->
    <App ref="appRef" />
    <!-- 任务设置 -->
    <Tasks ref="tasksRef" />
  </div>
</template>
<script lang="tsx" setup name="shopList">
import { reactive, ref } from "vue";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { Shop, getShopList, getShopLoginQR, getAccessToken, getAppList } from "@/views/shop/index";
import Login from "@/views/shop/components/Login.vue";
import App from "@/views/shop/components/App.vue";
import { ElMessage } from "element-plus";
const proTable = ref<ProTableInstance>();
const loginRef = ref<InstanceType<typeof Login> | null>(null);
const appRef = ref<InstanceType<typeof App> | null>(null);
// ProTable 实例

import Tasks from "@/views/components/tasks/Tasks.vue";
const tasksRef = ref();
const openDialogTasks = (name: string, method: string) => {
  tasksRef.value.acceptParams(name, method);
};

const openAppDialog = async () => {
  const form = {
    appName: "",
    appKey: "",
    appSecret: "",
    pin: "",
    redirectUri: ""
  };
  await getAppList().then(res => {
    form.appKey = res.data.appKey;
    form.appSecret = res.data.appSecret;
    form.pin = res.data.pin;
    form.redirectUri = res.data.redirectUri;
    form.appName = res.data.name;
  });
  appRef.value?.acceptParams({
    raw: {
      form,
      rules: {
        appName: [{ required: true, message: "请输入应用名称", trigger: "blur" }],
        appKey: [
          { required: true, message: "请输入appKey", trigger: "blur" },
          { min: 32, max: 32, message: "appKey长度为32位", trigger: "blur" }
        ],
        appSecret: [
          { required: true, message: "请输入appSecret", trigger: "blur" },
          { min: 32, max: 32, message: "appSecret长度为32位", trigger: "blur" }
        ],
        pin: [{ required: true, message: "请绑定账户", trigger: "blur" }],
        redirectUri: [{ required: true, message: "请输入回调地址", trigger: "blur" }]
      }
    }
  });
};

const columns = reactive<ColumnProps<Shop.List>[]>([
  { type: "selection", fixed: "left" },
  { type: "index", label: "序号", width: 70 },
  {
    prop: "status",
    label: "登录状态",
    render: scope => {
      if (scope.row.status) {
        return <el-tag type="success">正常</el-tag>;
      } else {
        return <el-tag type="danger">失效</el-tag>;
      }
    }
  },
  { prop: "name", label: "店铺名称" },
  { prop: "type", label: "店铺类型" },
  { prop: "level", label: "店铺层级" },
  { prop: "id", label: "店铺ID" },
  { prop: "venderId", label: "商家ID" },
  {
    prop: "accessToken",
    label: "授权状态",
    render: scope => {
      // const { accessToken } = scope.row;
      // if (!accessToken) {
      //   return (
      //     <el-tag style={{ cursor: "pointer" }} type="info" onClick={() => handleGetAccessToken("fetch", scope.row)}>
      //       获取授权
      //     </el-tag>
      //   );
      // }

      const isExpired = scope.row.isExpired;
      const expireTime = scope.row.expireTime;
      // 如果未null则表示未获取授权
      if (isExpired === null || expireTime === null) {
        return (
          <el-tag style={{ cursor: "pointer" }} type="info" onClick={() => handleGetAccessToken("fetch", scope.row)}>
            获取授权
          </el-tag>
        );
      }

      const tagType = isExpired ? "danger" : "success";
      const tagText = isExpired ? "刷新授权" : "已授权";
      const tooltipContent = "到期时间：" + scope.row.expireTime;
      {
        /* <el-tag style={{ cursor: "pointer" }} type={tagType} v-copy={scope.row.accessToken}> */
      }

      return (
        <el-tooltip class="box-item" effect="dark" content={tooltipContent} placement="bottom">
          {isExpired ? (
            <el-tag style={{ cursor: "pointer" }} type={tagType} onClick={() => handleGetAccessToken("refresh", scope.row)}>
              {/* 刷新授权 */}
              {tagText}
            </el-tag>
          ) : (
            <el-tag style={{ cursor: "pointer" }} type={tagType}>
              {/* 授权正常 */}
              {tagText}
            </el-tag>
          )}
        </el-tooltip>
      );
    }
  },
  { prop: "updateTime", label: "更新时间" },
  { prop: "operation", label: "操作", fixed: "right" }
]);

const handelShopLogin = () => {
  getShopLoginQR().then(res => {
    loginRef.value?.acceptParams({
      row: {
        key: res.data.key,
        image: res.data.image,
        getTableList: proTable.value?.getTableList
      }
    });
  });
};

const handleGetAccessToken = (action: string, row: Shop.List) => {
  getAccessToken({ shopId: row.id, action: action }).then(() => {
    ElMessage.success("操作成功");
    proTable.value?.getTableList();
  });
};
</script>
<style scoped>
.desc-box {
  display: flex;
  padding: 10px;
  margin-bottom: 20px;
  background: linear-gradient(90deg, #fff8eb, #ffffff);
  border-radius: 12px;
}
.desc-content {
  display: flex;
  flex: 1;
  flex-direction: column;
  font-size: 14px;
  line-height: 20px;
  color: #ff9702;
}
</style>
