<template>
  <div class="table-box card">
    <el-button @click="start">开始</el-button>
    <el-button @click="pause">暂停</el-button>
    <el-button @click="stop">停止</el-button>
    <el-button @click="status">状态</el-button>
  </div>
</template>

<script lang="ts" setup>
import http from "@/api";
const start = () => {
  console.log("start");
  http.post("/product-collect/start", {}).then(res => {
    console.log(res);
  });
};
// 暂停
const pause = () => {
  console.log("pause");
  http.post("/product-collect/pause", {}).then(res => {
    console.log(res);
  });
};
// 停止
const stop = () => {
  console.log("stop");
  http.post("/product-collect/stop", {}).then(res => {
    console.log(res);
  });
};
// 状态
const status = () => {
  http.get("/product-collect/status", {}).then(res => {
    console.log(res);
  });
};
</script>
