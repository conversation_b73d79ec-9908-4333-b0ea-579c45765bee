<template>
  <el-dialog
    v-model="dialogVisible"
    :title="taskInfo.name"
    width="550px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :destroy-on-close="true"
    :align-center="true"
    class="card"
  >
    <el-form label-suffix=":" style="width: 350px" label-width="100px" ref="formRef" :rules="rules" :model="form">
      <el-form-item label="状态" v-if="taskInfo.status">
        <el-text style="font-weight: bold" :type="taskInfo.enabled ? 'success' : 'danger'">
          {{ taskInfo.enabled ? "运行中" : "已停止" }}
        </el-text>
      </el-form-item>
      <div v-for="item in components" :key="item.id">
        <el-form-item :label="item.label" :prop="item.required ? item.id : undefined">
          <el-input v-if="item.type === 'input'" v-model="form[item.id]" :placeholder="'请输入' + item.label" />
          <!-- 多行文本 -->
          <el-input
            v-model="form[item.id]"
            v-if="item.type === 'textarea'"
            type="textarea"
            :placeholder="'请输入' + item.label"
            :autosize="{ minRows: 3, maxRows: 5 }"
          />
          <el-input-number
            v-if="item.type === 'input-number'"
            :min="1"
            v-model="form[item.id]"
            :placeholder="'请输入' + item.label"
          />
          <!-- 下拉选择 -->
          <el-select v-if="item.type === 'select'" v-model="form[item.id]" placeholder="请选择">
            <el-option v-for="child in item.options" :key="child.value" :label="child.label" :value="child.value" />
          </el-select>
        </el-form-item>
      </div>
      <!-- 按钮 -->
      <el-form-item style="margin-top: 30px">
        <el-button type="primary" @click="submitForm(formRef)">{{ taskInfo.status ? "更新" : "创建" }}</el-button>
        <el-button v-if="taskInfo.status" :type="taskInfo.enabled ? 'danger' : 'success'" @click="handleChangeState">
          {{ taskInfo.enabled ? "停止" : "开始" }}
        </el-button>
        <el-button v-if="taskInfo.status" type="danger" @click="handleDeleteTask">删除</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>
<script lang="ts" setup>
import { reactive, ref } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import { handleTasks } from "@/views/components/tasks/index";
const components = ref<{ [key: string]: any }[]>([]);
const form = reactive<{ [key: string]: any }>({});
const rules = reactive<{ [key: string]: any }>({});
// 任务信息
const taskInfo = reactive<{ [key: string]: any }>({});
const formRef = ref<FormInstance>();
const dialogVisible = ref(false);
const acceptParams = (name: string, method: string) => {
  // taskName.value = name;
  taskInfo.method = method;
  taskInfo.name = name;
  handleTasks("GET", { method: method })!.then((res: any) => {
    components.value = res.data.rules;
    (res.data.rules as any[]).forEach((item: any) => {
      if (item.required) {
        rules[item.id] = [{ required: true, message: "必选项", trigger: "blur" }];
      }
      if (item.type === "input-number") {
        form[item.id] = 1;
      } else {
        form[item.id] = "";
      }
    });
    // method.value = res.data.method;
    taskInfo.status = res.data.status;
    // 初始化任务信息
    // taskInfo.enabled = res.data.params.enabled;
    if (Object.keys(res.data.params).length > 0) {
      taskInfo.enabled = res.data.params.enabled;
      taskInfo.totalRunCount = res.data.params.totalRunCount;
      Object.keys(form).forEach(key => {
        form[key] = res.data.params.detail[key] ?? "";
      });
    }

    dialogVisible.value = true;
  });
};
defineExpose({
  acceptParams
});

const submitForm = (formEl: FormInstance | undefined) => {
  // 通过任务是否存在来判断是更新还是创建
  if (!formEl) return;
  formEl.validate((valid, fields) => {
    if (valid) {
      form.taskId = taskInfo.taskId;
      form.taskName = taskInfo.name;
      form.method = taskInfo.method;
      handleTasks("POST", form)?.then(() => {
        ElMessage.success("操作成功");
        dialogVisible.value = false;
      });
    } else {
      console.log("error submit!", fields);
    }
  });
};
const handleChangeState = () => {
  handleTasks("UPDATE", { enabled: !taskInfo.enabled, method: taskInfo.method })!.then(() => {
    ElMessage.success(taskInfo.enabled ? "停止成功" : "启动成功");
    taskInfo.enabled = !taskInfo.enabled;
  });
};
const handleDeleteTask = () => {
  handleTasks("DELETE", { method: taskInfo.method })?.then(() => {
    ElMessage.success("删除成功");
    dialogVisible.value = false;
  });
};
</script>
<style scoped>
.el-textarea,
.el-select {
  width: 100% !important;
}
</style>
