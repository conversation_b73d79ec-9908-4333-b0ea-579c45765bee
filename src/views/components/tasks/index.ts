import http from "@/api";

export const options = [
  {
    value: "seconds",
    label: "秒"
  },
  {
    value: "minutes",
    label: "分钟"
  },
  {
    value: "hours",
    label: "小时"
  },
  {
    value: "days",
    label: "天"
  }
];

export const getWechatContact = (params?: any) => {
  return http.get<{ [key: string]: any }>("/task/wechat/contact", params);
};

export const handleTasks = (action: string, params?: any) => {
  const url = "/tasks";
  switch (action) {
    case "GET":
      return http.get<{ [key: string]: any }>(url, params);
    case "POST":
      return http.post<{ [key: string]: any }>(url, params);
    case "UPDATE":
      return http.put<{ [key: string]: any }>(url, params);
    case "DELETE":
      return http.delete<{ [key: string]: any }>(url, params);
  }
};
