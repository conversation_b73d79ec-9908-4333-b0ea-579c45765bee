<!-- components/CommonDialog.vue -->
<template>
  <el-dialog v-model="dialogVisible" :title="title" width="550px" :close-on-click-modal="false" :destroy-on-close="true">
    <slot />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from "vue";

const dialogVisible = ref(false);
const title = ref("Dialog");

const show = (newTitle: string) => {
  title.value = newTitle;
  dialogVisible.value = true;
};

defineExpose({
  show
});
</script>
