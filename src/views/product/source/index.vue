<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      :columns="columns"
      :request-api="getProductSourceList"
      :search-col="6"
      highlight-current-row
      :tool-button="true"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button type="primary" @click="openDialogTasks('平台账号', 'go2.login')">商品发布</el-button>
        <el-button type="primary" @click="openDialogTasks('货源自动采集设置', 'product.collection')">货源状态监控</el-button>
      </template>
      <!-- 表格操作 -->
      <template #operation="scope">
        <el-button type="primary">发布商品</el-button>
        <el-button type="primary" plain @click="handleSourceDetail('detail', scope.row.pid)">详情</el-button>
        <el-button type="danger" plain @click="handleDelete(scope.row)">删除</el-button>
      </template>
    </ProTable>

    <!-- 货源详情 -->
    <SourceDetail ref="sourceDetailRef" />
    <!-- 任务设置 -->
    <Tasks ref="tasksRef" />
  </div>
</template>
<script lang="tsx" setup name="productCollection">
import { ref, reactive, onMounted } from "vue";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { ElMessage, ElMessageBox } from "element-plus";
import { Product } from "@/views/product/interface/index";
import { getProductSourceList } from "@/views/product/interface/source";
import SourceDetail from "@/views/product/components/Detail.vue";
import Tasks from "@/views/components/tasks/Tasks.vue";
import { Shop, getShopList } from "@/views/shop/index";
const shopList = ref<Shop.List[]>([]);
const shopTotal = ref(0);

onMounted(async () => {
  try {
    const res = await getShopList();
    shopList.value = res.data.list;
    shopTotal.value = res.data.total;
  } catch (error) {
    console.error("获取店铺列表失败", error);
    ElMessage.error("获取店铺列表失败");
  }
});

const tasksRef = ref();
const openDialogTasks = (name: string, method: string) => {
  tasksRef.value.acceptParams(name, method);
};
const sourceDetailRef = ref<InstanceType<typeof SourceDetail> | null>(null);
const proTable = ref<ProTableInstance>();
const columns = reactive<ColumnProps<Product.Source>[]>([
  { type: "selection", fixed: "left" },
  { type: "index", label: "序号", width: 70 },
  { prop: "brand", label: "名称" },
  {
    prop: "size",
    label: "尺码",
    render: scope => {
      return scope.row.size.join("/");
    }
  },
  {
    prop: "articleNumber",
    label: "档口货号",
    render: scope => {
      return (
        <a
          class="table-button"
          onClick={() => {
            window.open(`https://www.go2.cn/product/${scope.row.pid}.html`);
          }}
        >
          {scope.row.articleNumber}
        </a>
      );
    }
  },
  { prop: "price", label: "价格" },
  {
    prop: "categories",
    label: "分类",
    render: scope => {
      const category = handleFormatCategory(scope.row.categories[0].path);
      return category;
    }
  },
  {
    prop: "props",
    label: "商品属性",
    render: scope => {
      console.log(scope.row.categories);
      return "点击查看";
    }
  },
  {
    prop: "products",
    label: "发布进度",
    render: scope => {
      const progress = scope.row.products.length;
      // 店铺总数/已经发布的总数
      return (
        <>
          <el-text>{progress}</el-text>/<el-text>{shopTotal.value}</el-text>
        </>
      );
    }
  },
  { prop: "createTime", label: "入库时间" },
  { prop: "operation", label: "操作", fixed: "right", width: 280 }
]);
// 辅助函数用于生成时间范围
const getDateRange = (days: number) => {
  const end = new Date(); // 当前时间
  const start = new Date();
  start.setDate(start.getDate() - days);
  return [start, end];
};
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const shortcuts = [
  {
    text: "今天",
    value: () => getDateRange(0) // 显示今天
  },
  {
    text: "昨天",
    value: () => getDateRange(1) // 显示昨天
  },
  {
    text: "上周",
    value: () => getDateRange(7) // 显示过去一周
  }
];

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const handleDelete = (row: Product.Source) => {
  ElMessageBox.confirm("确定删除该商品吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(() => {
      deleteProductCollection(row.id).then(() => {
        proTable.value?.getTableList();
        ElMessage({
          type: "success",
          message: "删除成功"
        });
      });
    })
    .catch(() => {
      ElMessage({});
    });
};

// 货源详情
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const handleSourceDetail = (action: string, pid: string) => {
  sourceDetailRef.value?.acceptParams(action, pid);
};
// 格式化分类
const handleFormatCategory = (category: string[]) => {
  // 从第三位开始截取数组（索引为 2 的元素开始）
  const subCategories = category.slice(2);
  // 使用 '/' 拼接并返回
  return subCategories.join("->");
};
</script>
<style>
.el-row {
  margin-bottom: 20px;
}
.el-row:last-child {
  margin-bottom: 0;
}
.el-col {
  border-radius: 4px;
}
.grid-content {
  min-height: 36px;
  background-color: #ffffff;
  border-radius: 4px;
}
.card_box {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  height: 80px;
  border-radius: 4px;
  -webkit-box-align: center;
  .card_box_cir {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    -webkit-box-pack: center;
    -webkit-box-align: center;
    .size {
      margin-right: 20px;
      margin-left: 20px;
      font-size: 45px;
    }
  }
  .card_box_txt {
    .sp1 {
      display: block;
      font-size: 24px;
      color: rgb(37 38 49);
    }
    .sp2 {
      display: block;
      font-size: 12px;
      color: rgb(152 169 188);
    }
  }
}
</style>
