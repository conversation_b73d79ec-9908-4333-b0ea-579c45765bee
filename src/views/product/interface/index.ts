export namespace Product {
  export interface Collection {
    id: number;
    gid: number;
    userId: number;
    pid: string;
    brand: string;
    articleNumber: string;
    price: string;
    image: string;
    color: string[];
    size: string[];
    productId: string;
    state: boolean;
    imagePath: string;
    draft: string;
    spu: number;
    productPrice: string;
    status: boolean;
    shop: any;
    maxPrice: string;
    path: string[];
  }
  export interface Source extends Collection {
    categories: Category[];
    products: ProductDetail[];
  }
  export interface Category {
    id: number;
    platform: number;
    category: number[];
    props: { [key: string]: any }[];
    path: string[];
    updateTime: string;
  }
  export interface ProductDetail {
    id: number;
    spu: string;
    shopId: number;
    categoryId: number;
  }
}
