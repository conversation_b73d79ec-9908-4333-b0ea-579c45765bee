import http from "@/api";
import { ResPage } from "@/api/interface";
import { Product } from "./index";

// 采集任务状态
export const getCollectStatus = () => {
  return http.get<{ [key: string]: any }>(`/product-collect/status`);
};

// 货源管理列表
export const getProductSourceList = (params: any) => {
  return http.get<{ [key: string]: any }>(`/product/source/list`, params);
};

// 货源采集
export const getProductCollectionList = (params: any) => {
  return http.get<ResPage<Product.Collection>>(`/product/source/collection`, params);
};

// 删除待入库商品
export const deleteProductCollection = (id: number) => {
  return http.delete<{ [key: string]: any }>("/product/source/collection", { id: id });
};

// 商品入库
export const handleProductIn = (params: any) => {
  return http.post<{ [key: string]: any }>("/product/source/insertion", params);
};
