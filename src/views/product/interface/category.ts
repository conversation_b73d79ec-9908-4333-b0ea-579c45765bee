import http from "@/api";
export interface CategoryData {
  fid: number;
  aliasName: string;
  created: number;
  name: string;
  modified: number;
  indexId: number;
  id: number;
  lev: number;
  status: number;
}

export interface CategoryTreeNode {
  label: string;
  value: number;
  children?: CategoryTreeNode[];
}
export const getShopCategoryList = async (params: any) => {
  const res = await http.get("/jd/category/getFullValidCategoryResultByVenderId", params);
  const data = res.data as CategoryData[];
  const categoryMap: { [key: number]: CategoryTreeNode } = {};
  // 将每个项映射为一个 CategoryTreeNode 对象，并存储在 categoryMap 中
  data.forEach(item => {
    categoryMap[item.id] = {
      label: item.name,
      value: item.id,
      children: [] // 初始化为空
    };
  });
  // 构建树形结构
  const result: CategoryTreeNode[] = [];
  data.forEach(item_1 => {
    if (item_1.fid === 0) {
      // 一级目录
      result.push(categoryMap[item_1.id]);
    } else {
      // 子项
      const parentNode = categoryMap[item_1.fid];
      if (parentNode) {
        parentNode.children?.push(categoryMap[item_1.id]);
      }
    }
  });
  return result;
};
export const JdCategory = [
  {
    level: 3,
    label: "雨鞋/雨靴",
    value: 9777
  },
  {
    level: 3,
    label: "女士单鞋",
    value: 6914,
    children: [
      {
        level: 4,
        label: "女士乐福鞋/豆豆鞋",
        value: 31796
      },
      {
        level: 4,
        label: "女士穆勒鞋",
        value: 31797
      },
      {
        level: 4,
        label: "女士玛丽珍鞋",
        value: 31798
      },
      {
        level: 4,
        label: "女士船鞋",
        value: 31799
      },
      {
        level: 4,
        label: "女士高跟鞋",
        value: 31800
      },
      {
        level: 4,
        label: "其它女士单鞋",
        value: 31801
      }
    ]
  },
  {
    level: 3,
    label: "鞋配件",
    value: 9779
  },
  {
    level: 3,
    label: "女士休闲鞋",
    value: 6916,
    children: [
      {
        level: 4,
        label: "女士老爹鞋",
        value: 31792
      },
      {
        level: 4,
        label: "女士板鞋",
        value: 31793
      },
      {
        level: 4,
        label: "女士帆布鞋",
        value: 31794
      },
      {
        level: 4,
        label: "其它女士休闲鞋",
        value: 31795
      },
      {
        level: 4,
        label: "女士网面鞋",
        value: 31832
      }
    ]
  },
  {
    level: 3,
    label: "女士凉鞋",
    value: 6917,
    children: [
      {
        level: 4,
        label: "女士洞洞鞋",
        value: 31802,
        isLeaf: true
      },
      {
        level: 4,
        label: "女士罗马凉鞋",
        value: 31803
      },
      {
        level: 4,
        label: "女士沙滩鞋",
        value: 31804
      },
      {
        level: 4,
        label: "女士一字带凉鞋",
        value: 31805
      },
      {
        level: 4,
        label: "其它女士凉鞋",
        value: 31806
      }
    ]
  },
  {
    level: 3,
    label: "布鞋/绣花鞋",
    value: 6918
  },
  {
    level: 3,
    label: "赠品",
    value: 12426
  },
  {
    level: 3,
    label: "女士拖鞋",
    value: 9775,
    children: [
      {
        level: 4,
        label: "女士包头拖",
        value: 31807
      },
      {
        level: 4,
        label: "女士人字拖/套趾拖",
        value: 31808
      },
      {
        level: 4,
        label: "女士一字拖/十字拖",
        value: 31809
      },
      {
        level: 4,
        label: "女士棉拖",
        value: 31810
      }
    ]
  },
  {
    level: 3,
    label: "特殊商品",
    value: 13184
  },
  {
    level: 3,
    label: "女靴",
    value: 9776,
    children: [
      {
        level: 4,
        label: "女士马丁靴",
        value: 30889
      },
      {
        level: 4,
        label: "女士雪地靴",
        value: 30890
      },
      {
        level: 4,
        label: "女士时装靴",
        value: 30891
      },
      {
        level: 4,
        label: "弹力靴/袜靴",
        value: 30892
      },
      {
        level: 4,
        label: "女士切尔西靴",
        value: 30893
      }
    ]
  }
];
export const JdCategoryb = [
  {
    label: "鞋靴",
    value: 11729,
    children: [
      {
        label: "时尚女鞋",
        value: 11731,
        children: [
          {
            level: 3,
            label: "雨鞋/雨靴",
            value: 9777
          },
          {
            level: 3,
            label: "女士单鞋",
            value: 6914,
            children: [
              {
                level: 4,
                label: "女士乐福鞋/豆豆鞋",
                value: 31796
              },
              {
                level: 4,
                label: "女士穆勒鞋",
                value: 31797
              },
              {
                level: 4,
                label: "女士玛丽珍鞋",
                value: 31798
              },
              {
                level: 4,
                label: "女士船鞋",
                value: 31799
              },
              {
                level: 4,
                label: "女士高跟鞋",
                value: 31800
              },
              {
                level: 4,
                label: "其它女士单鞋",
                value: 31801
              }
            ]
          },
          {
            level: 3,
            label: "鞋配件",
            value: 9779
          },
          {
            level: 3,
            label: "女士休闲鞋",
            value: 6916,
            children: [
              {
                level: 4,
                label: "女士老爹鞋",
                value: 31792
              },
              {
                level: 4,
                label: "女士板鞋",
                value: 31793
              },
              {
                level: 4,
                label: "女士帆布鞋",
                value: 31794
              },
              {
                level: 4,
                label: "其它女士休闲鞋",
                value: 31795
              },
              {
                level: 4,
                label: "女士网面鞋",
                value: 31832
              }
            ]
          },
          {
            level: 3,
            label: "女士凉鞋",
            value: 6917,
            children: [
              {
                level: 4,
                label: "女士洞洞鞋",
                value: 31802,
                isLeaf: true
              },
              {
                level: 4,
                label: "女士罗马凉鞋",
                value: 31803
              },
              {
                level: 4,
                label: "女士沙滩鞋",
                value: 31804
              },
              {
                level: 4,
                label: "女士一字带凉鞋",
                value: 31805
              },
              {
                level: 4,
                label: "其它女士凉鞋",
                value: 31806
              }
            ]
          },
          {
            level: 3,
            label: "布鞋/绣花鞋",
            value: 6918
          },
          {
            level: 3,
            label: "赠品",
            value: 12426
          },
          {
            level: 3,
            label: "女士拖鞋",
            value: 9775,
            children: [
              {
                level: 4,
                label: "女士包头拖",
                value: 31807
              },
              {
                level: 4,
                label: "女士人字拖/套趾拖",
                value: 31808
              },
              {
                level: 4,
                label: "女士一字拖/十字拖",
                value: 31809
              },
              {
                level: 4,
                label: "女士棉拖",
                value: 31810
              }
            ]
          },
          {
            level: 3,
            label: "特殊商品",
            value: 13184
          },
          {
            level: 3,
            label: "女靴",
            value: 9776,
            children: [
              {
                level: 4,
                label: "女士马丁靴",
                value: 30889
              },
              {
                level: 4,
                label: "女士雪地靴",
                value: 30890
              },
              {
                level: 4,
                label: "女士时装靴",
                value: 30891
              },
              {
                level: 4,
                label: "弹力靴/袜靴",
                value: 30892
              },
              {
                level: 4,
                label: "女士切尔西靴",
                value: 30893
              }
            ]
          }
        ]
      }
    ]
  }
];

export const DouDianCategory = [
  {
    label: "女鞋",
    value: 20006,
    children: [
      {
        value: 20225,
        label: "低帮鞋",
        level: 2,
        children: [
          {
            value: 22450,
            label: "布鞋",
            level: 3
          },
          {
            value: 22449,
            label: "单鞋",
            level: 3
          },
          {
            value: 36986,
            label: "汉服鞋",
            level: 3
          },
          {
            value: 22459,
            label: "乐福鞋/豆豆鞋",
            level: 3
          },
          {
            value: 36985,
            label: "棉鞋",
            level: 3
          },
          {
            value: 22455,
            label: "穆勒鞋",
            level: 3
          },
          {
            value: 22454,
            label: "牛津鞋",
            level: 3
          },
          {
            value: 22462,
            label: "时尚休闲鞋",
            level: 3
          },
          {
            value: 22453,
            label: "松糕/摇摇鞋",
            level: 3
          },
          {
            value: 22460,
            label: "休闲板鞋",
            level: 3
          }
        ]
      },
      {
        value: 20226,
        label: "帆布鞋",
        level: 2
      },
      {
        value: 20227,
        label: "高帮鞋",
        level: 2
      },
      {
        value: 20228,
        label: "高跟鞋",
        level: 2,
        children: [
          {
            value: 22463,
            label: "方根高跟鞋",
            level: 3
          },
          {
            value: 36974,
            label: "婚鞋",
            level: 3
          },
          {
            value: 22456,
            label: "坡跟鞋",
            level: 3
          },
          {
            value: 22464,
            label: "细跟高跟鞋",
            level: 3
          }
        ]
      },
      {
        value: 20184,
        label: "凉鞋",
        level: 2,
        children: [
          {
            value: 22373,
            label: "洞洞鞋",
            level: 3
          },
          {
            value: 34718,
            label: "罗马凉鞋",
            level: 3
          },
          {
            value: 22420,
            label: "时尚休闲沙滩鞋",
            level: 3
          },
          {
            value: 22405,
            label: "时装凉鞋",
            level: 3
          },
          {
            value: 34717,
            label: "一字带凉鞋",
            level: 3
          }
        ]
      },
      {
        value: 20249,
        label: "女鞋配件",
        level: 2
      },
      {
        value: 20185,
        label: "拖鞋",
        level: 2,
        children: [
          {
            value: 22376,
            label: "包头拖",
            level: 3
          },
          {
            value: 34716,
            label: "棉拖",
            level: 3
          },
          {
            value: 22375,
            label: "人字拖",
            level: 3
          },
          {
            value: 22374,
            label: "一字拖",
            level: 3
          }
        ]
      },
      {
        value: 20187,
        label: "靴子",
        level: 2,
        children: [
          {
            value: 22400,
            label: "弹力靴/袜靴",
            level: 3
          },
          {
            value: 22335,
            label: "短靴",
            level: 3
          },
          {
            value: 22378,
            label: "马丁靴",
            level: 3
          },
          {
            value: 36987,
            label: "棉靴",
            level: 3
          },
          {
            value: 22406,
            label: "切尔西靴",
            level: 3
          },
          {
            value: 22416,
            label: "时尚雪地靴",
            level: 3
          },
          {
            value: 22377,
            label: "时装靴",
            level: 3
          }
        ]
      },
      {
        value: 20186,
        label: "雨鞋",
        level: 2
      }
    ]
  }
];

export const findLabels = (categoryList: any, values: any) => {
  const labels: any[] = [];

  function recursiveSearch(list: any, values: string | any[]) {
    for (const item of list) {
      if (values.includes(item.value)) {
        labels.push(item.label);
        if (item.children) {
          recursiveSearch(item.children, values);
        }
      }
    }
  }

  recursiveSearch(categoryList, values);
  return labels;
};

// 获取商品属性
export const getJdCategoryProps = (params: any) => {
  // console.log(params);
  return http.post<{ [key: string]: any }>("/jd/category/props", params);
};

export const handleOcrProductCategory = (params: any) => {
  return http.get<{ [key: string]: any }>("/product/source/category/ocrCategory", params);
};

// 获取商品属性列表
export const findAttrsByCategoryIdUnlimitCate = (params: any) => {
  return http.get<{ [key: string]: any }>(`/product/category/findAttrsByCategoryIdUnlimitCate`, params);
};
