import http from "@/api";

export const getProductSourceDetail = (params: any) => {
  return http.get<{ [key: string]: any }>(`/product/source/detail`, params);
};

export const downloadProductImage = (pid: string) => {
  return http.get<{ [key: string]: any }>(`/product/source/detail/download`, { pid: pid });
};

export const downloadProductImageByCode = (pid: string, index: number) => {
  return http.get<{ [key: string]: any }>(`/product/source/detail/downloadByCode`, { pid: pid, index: index });
};
