import http from "@/api";

export const getShopInfoOptions = (params: any) => {
  return http.get<{ [key: string]: any }>(`/product/publish/options`, params);
};

// 发布商品-搜索产品
export const searchProduct = (params: any) => {
  return http.get<{ [key: string]: any }>(`/product/publish/query`, params);
};
// 发布商品
export const handleProdctPublish = (params: any) => {
  return http.post<{ [key: string]: any }>(`/product/publish`, params);
};
// 生成短标题
export const getGenerateShortTitle = (params: any) => {
  return http.post<{ [key: string]: any }>(`/product/publish/short-title`, params);
};
