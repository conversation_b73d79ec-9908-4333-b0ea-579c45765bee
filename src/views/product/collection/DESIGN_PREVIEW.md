# 采集设置界面设计预览 - 已更新

## ✅ 根据你的要求已完成以下调整：

### 1. 🚫 移除自定义字体

- 去除了 `font-family` 自定义字体设置
- 使用系统默认字体，保持一致性

### 2. 📏 减少界面高度

- Dialog宽度从700px调整为600px
- 减少各区域间距：32px → 20px
- 表单项间距：20px → 16px
- 统计数字大小：24px → 20px

### 3. 📋 采集配置改为表单形式

- 使用 `el-form` 和 `el-form-item` 标准表单组件
- 从上到下垂直排列，不再使用两列布局
- 每个配置项独占一行，更清晰

### 4. 🎨 任务状态使用颜色点展示

- **不再使用文字标签**，改用彩色圆点
- 状态指示器：8px圆点 + 状态文字
- 颜色方案：
  - 🟢 成功/运行中：#67c23a (绿色)
  - 🟠 警告/暂停：#e6a23c (橙色)
  - 🔵 信息/停止：#909399 (灰色)
  - 🔴 错误：#f56c6c (红色)

## 🎨 设计细节

### 颜色方案

- **主色调**: #409eff (蓝色)
- **成功色**: #67c23a (绿色)
- **警告色**: #e6a23c (橙色)
- **危险色**: #f56c6c (红色)
- **文字色**: #333 (深灰) / #666 (中灰) / #999 (浅灰)

### 字体规范

- **标题**: 16px, 500 weight
- **标签**: 14px, 400 weight
- **数值**: 24px, 600 weight (统计数字)
- **按钮**: 14px, 500 weight

### 间距规范

- **区域间距**: 32px
- **元素间距**: 20px / 8px
- **按钮间距**: 12px

## 🔧 技术实现

### 组件结构

```
CollectSettings.vue
├── 任务状态区域 (status-section)
│   ├── 状态标签行 (status-row)
│   ├── 统计数据行 (stats-row)
│   └── 时间信息行 (time-row)
├── 采集配置区域 (config-section)
│   └── 表单 (config-form)
│       ├── 页码/每页数量
│       ├── 排序/间隔
│       ├── 价格/关键词
│       └── API地址
└── 底部按钮 (dialog-footer)
```

### 样式特色

- **Flexbox布局**: 灵活的响应式布局
- **CSS变量**: 统一的颜色和尺寸管理
- **深度选择器**: 精确控制Element Plus组件样式
- **状态驱动**: 根据任务状态动态显示不同按钮

## 📋 使用方法

1. 点击"采集设置"按钮打开弹窗
2. 查看当前任务状态和统计信息
3. 修改采集配置参数
4. 点击"启动采集"开始任务
5. 点击"保存配置"保存设置

## 🎯 设计优势

- **用户友好**: 信息展示清晰，操作流程简单
- **视觉统一**: 与项目整体设计风格保持一致
- **功能完整**: 涵盖状态查看、配置修改、任务控制
- **响应迅速**: 实时状态更新，操作反馈及时

这个新设计完全按照你提供的界面图进行实现，简洁美观，功能完整！
