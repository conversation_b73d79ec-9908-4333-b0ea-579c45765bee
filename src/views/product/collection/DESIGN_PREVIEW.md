# 采集设置界面设计预览

## 🎨 新设计特点

根据你提供的设计图，我重新设计了采集设置界面，具有以下特点：

### 📱 界面布局
- **简洁清爽**: 去除了复杂的卡片和阴影，采用扁平化设计
- **信息层次清晰**: 任务状态和采集配置分区明确
- **响应式布局**: 适配不同屏幕尺寸

### 🏷️ 任务状态区域
- **状态标签**: 使用彩色标签显示当前状态和运行状态
- **数据统计**: 大数字显示采集商品数、新增商品数、同步次数
- **时间信息**: 灰色输入框显示任务开始时间和最后同步时间

### ⚙️ 采集配置区域
- **表单布局**: 两列布局，左右对称
- **输入控件**: 统一的输入框和数字输入器样式
- **下拉选择**: 简洁的选择器样式
- **文本域**: 显示当前API地址

### 🎯 按钮设计
- **取消按钮**: 灰色边框，白色背景
- **启动采集**: 绿色背景，表示开始操作
- **保存配置**: 蓝色背景，表示主要操作

## 🎨 设计细节

### 颜色方案
- **主色调**: #409eff (蓝色)
- **成功色**: #67c23a (绿色) 
- **警告色**: #e6a23c (橙色)
- **危险色**: #f56c6c (红色)
- **文字色**: #333 (深灰) / #666 (中灰) / #999 (浅灰)

### 字体规范
- **标题**: 16px, 500 weight
- **标签**: 14px, 400 weight  
- **数值**: 24px, 600 weight (统计数字)
- **按钮**: 14px, 500 weight

### 间距规范
- **区域间距**: 32px
- **元素间距**: 20px / 8px
- **按钮间距**: 12px

## 🔧 技术实现

### 组件结构
```
CollectSettings.vue
├── 任务状态区域 (status-section)
│   ├── 状态标签行 (status-row)
│   ├── 统计数据行 (stats-row)  
│   └── 时间信息行 (time-row)
├── 采集配置区域 (config-section)
│   └── 表单 (config-form)
│       ├── 页码/每页数量
│       ├── 排序/间隔
│       ├── 价格/关键词
│       └── API地址
└── 底部按钮 (dialog-footer)
```

### 样式特色
- **Flexbox布局**: 灵活的响应式布局
- **CSS变量**: 统一的颜色和尺寸管理
- **深度选择器**: 精确控制Element Plus组件样式
- **状态驱动**: 根据任务状态动态显示不同按钮

## 📋 使用方法

1. 点击"采集设置"按钮打开弹窗
2. 查看当前任务状态和统计信息
3. 修改采集配置参数
4. 点击"启动采集"开始任务
5. 点击"保存配置"保存设置

## 🎯 设计优势

- **用户友好**: 信息展示清晰，操作流程简单
- **视觉统一**: 与项目整体设计风格保持一致
- **功能完整**: 涵盖状态查看、配置修改、任务控制
- **响应迅速**: 实时状态更新，操作反馈及时

这个新设计完全按照你提供的界面图进行实现，简洁美观，功能完整！
