# 采集设置组件使用说明

## 组件功能

这个采集设置组件 (`CollectSettings.vue`) 提供了一个完整的采集任务管理界面，包括：

### 1. 任务状态展示
- **当前状态**: 显示任务是否运行中、已暂停、已停止等
- **运行状态**: 显示任务的实时运行状态
- **统计信息**: 显示已采集商品数、新增商品数、同步次数
- **时间信息**: 显示任务开始时间和最后同步时间

### 2. 采集配置管理
- **页码设置**: 设置采集的起始页码
- **每页数量**: 设置每页采集的商品数量 (1-100)
- **排序方式**: 支持最新排序、价格排序、销量排序
- **采集间隔**: 设置采集间隔时间 (10-3600秒)
- **价格筛选**: 支持价格范围筛选 (如: 100-500)
- **关键词搜索**: 支持按关键词搜索商品
- **API地址**: 显示当前使用的API地址

### 3. 任务控制功能
- **启动采集**: 开始采集任务
- **暂停采集**: 暂停正在运行的采集任务
- **停止采集**: 完全停止采集任务
- **保存配置**: 保存当前的采集配置

## 使用方法

### 在父组件中引入和使用

```vue
<template>
  <div>
    <!-- 触发按钮 -->
    <el-button type="primary" @click="openCollectSettings">采集设置</el-button>
    
    <!-- 采集设置组件 -->
    <CollectSettings ref="collectSettingsRef" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import CollectSettings from './CollectSettings.vue'

const collectSettingsRef = ref()

const openCollectSettings = () => {
  collectSettingsRef.value?.openDialog()
}
</script>
```

## API接口

组件依赖以下API接口：

1. **获取采集状态**: `GET /product-collect/status`
2. **启动采集**: `POST /product-collect/start`
3. **暂停采集**: `POST /product-collect/pause`
4. **停止采集**: `POST /product-collect/stop`
5. **保存配置**: `PUT /product-collect/config`

## 数据结构

### 采集状态返回数据
```json
{
  "isPaused": false,
  "isStopped": true,
  "lastSyncTime": 1753340564978,
  "isRunning": false,
  "success": true,
  "collectedProductCount": 3,
  "newProductCount": 122,
  "syncCount": 278,
  "currentConfig": {
    "pn": 1,
    "ps": 20,
    "sort": "newestSort",
    "price": null,
    "q": null,
    "interval": 60
  },
  "currentApiUrl": "http://seller.app.go2.cn/seller/wechat/products?pn=1&ps=20&sort=newestSort",
  "taskStatus": "STOPPED",
  "taskStartTime": 1753339073659
}
```

## 设计特点

- **响应式布局**: 使用 Element Plus 的栅格系统，适配不同屏幕尺寸
- **状态可视化**: 使用不同颜色的标签和统计组件展示状态
- **表单验证**: 完整的表单验证规则，确保数据有效性
- **加载状态**: 所有操作都有加载状态提示
- **错误处理**: 完善的错误处理和用户提示
- **设计一致性**: 遵循项目的设计规范，与 EditGroup.vue 保持一致的风格

## 样式特色

- 圆角设计，现代化界面
- 清晰的分区布局
- 统一的颜色主题
- 响应式交互效果
- 专业的表单样式
