<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      title="采集设置"
      width="600px"
      :draggable="true"
      :align-center="true"
      :destroy-on-close="true"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="resetForm"
    >
      <div class="settings-container">
        <!-- 任务状态区域 -->
        <div class="status-section">
          <h3 class="section-title">任务状态</h3>

          <div class="status-grid">
            <div class="status-item">
              <span class="status-label">当前状态</span>
              <div class="status-indicator">
                <div :class="['status-dot', getStatusClass(form.taskStatus)]"></div>
                <span class="status-text">{{ getStatusText(form.taskStatus) }}</span>
              </div>
            </div>
            <div class="status-item">
              <span class="status-label">运行状态</span>
              <div class="status-indicator">
                <div :class="['status-dot', form.isRunning ? 'status-success' : 'status-info']"></div>
                <span class="status-text">{{ form.isRunning ? "运行中" : "未运行" }}</span>
              </div>
            </div>
          </div>

          <div class="stats-grid">
            <div class="stat-item">
              <span class="stat-label">已采集商品</span>
              <span class="stat-value">{{ form.collectedProductCount }}个</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">新增商品</span>
              <span class="stat-value">{{ form.newProductCount }}个</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">同步次数</span>
              <span class="stat-value">{{ form.syncCount }}次</span>
            </div>
          </div>

          <div class="time-grid">
            <div class="time-item">
              <span class="time-label">任务开始时间</span>
              <el-input :value="formatTime(form.taskStartTime)" disabled placeholder="暂无" />
            </div>
            <div class="time-item">
              <span class="time-label">最后同步时间</span>
              <el-input :value="formatTime(form.lastSyncTime)" disabled placeholder="暂无" />
            </div>
          </div>
        </div>

        <!-- 采集配置区域 -->
        <div class="config-section">
          <h3 class="section-title">采集配置</h3>

          <el-form :model="form" ref="formRef" :rules="rules" label-position="top" class="config-form">
            <el-form-item label="页码" prop="currentConfig.pn">
              <el-input-number
                v-model="form.currentConfig.pn"
                :min="1"
                :max="999"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>

            <el-form-item label="每页数量" prop="currentConfig.ps">
              <el-input-number
                v-model="form.currentConfig.ps"
                :min="1"
                :max="100"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>

            <el-form-item label="排序方式" prop="currentConfig.sort">
              <el-select v-model="form.currentConfig.sort" style="width: 100%">
                <el-option label="最新排序" value="newestSort" />
                <el-option label="价格排序" value="priceSort" />
                <el-option label="销量排序" value="salesSort" />
              </el-select>
            </el-form-item>

            <el-form-item label="采集间隔(秒)" prop="currentConfig.interval">
              <el-input-number
                v-model="form.currentConfig.interval"
                :min="10"
                :max="3600"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>

            <el-form-item label="价格筛选">
              <el-input v-model="form.currentConfig.price" placeholder="如: 100-500" clearable />
            </el-form-item>

            <el-form-item label="关键词搜索">
              <el-input v-model="form.currentConfig.q" placeholder="请输入搜索关键词" clearable />
            </el-form-item>

            <el-form-item label="当前API地址">
              <el-input v-model="form.currentApiUrl" disabled type="textarea" :autosize="{ minRows: 2, maxRows: 3 }" />
            </el-form-item>
          </el-form>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false" :disabled="loading" class="cancel-btn">取消</el-button>
          <el-button v-if="!form.isRunning" type="success" @click="handleStart" :loading="loading" class="start-btn">
            启动采集
          </el-button>
          <el-button type="primary" @click="submitForm" :loading="loading" class="save-btn"> 保存配置 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from "vue";
import { ElMessage, type FormInstance } from "element-plus";
import http from "@/api";
import { getCollectStatus } from "@/views/product/interface/source";

const dialogVisible = ref(false);
const formRef = ref<FormInstance>();
const loading = ref(false);

interface CollectConfig {
  pn: number;
  ps: number;
  sort: string;
  price: string | null;
  q: string | null;
  interval: number;
}

interface CollectStatus {
  isPaused: boolean;
  isStopped: boolean;
  lastSyncTime: number;
  isRunning: boolean;
  success: boolean;
  collectedProductCount: number;
  newProductCount: number;
  syncCount: number;
  currentConfig: CollectConfig;
  currentApiUrl: string;
  taskStatus: string;
  taskStartTime: number;
}

const form = reactive<CollectStatus>({
  isPaused: false,
  isStopped: true,
  lastSyncTime: 0,
  isRunning: false,
  success: true,
  collectedProductCount: 0,
  newProductCount: 0,
  syncCount: 0,
  currentConfig: {
    pn: 1,
    ps: 20,
    sort: "newestSort",
    price: null,
    q: null,
    interval: 60
  },
  currentApiUrl: "",
  taskStatus: "STOPPED",
  taskStartTime: 0
});

// 表单验证规则
const rules = {
  "currentConfig.pn": [
    { required: true, message: "请输入页码", trigger: "blur" },
    { type: "number", min: 1, message: "页码必须大于0", trigger: "blur" }
  ],
  "currentConfig.ps": [
    { required: true, message: "请输入每页数量", trigger: "blur" },
    { type: "number", min: 1, max: 100, message: "每页数量在1-100之间", trigger: "blur" }
  ],
  "currentConfig.sort": [{ required: true, message: "请选择排序方式", trigger: "change" }],
  "currentConfig.interval": [
    { required: true, message: "请输入采集间隔", trigger: "blur" },
    { type: "number", min: 10, max: 3600, message: "采集间隔在10-3600秒之间", trigger: "blur" }
  ]
};

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case "RUNNING":
      return "success";
    case "PAUSED":
      return "warning";
    case "STOPPED":
      return "info";
    case "ERROR":
      return "danger";
    default:
      return "info";
  }
};

// 获取状态颜色类
const getStatusClass = (status: string) => {
  switch (status) {
    case "RUNNING":
      return "status-success";
    case "PAUSED":
      return "status-warning";
    case "STOPPED":
      return "status-info";
    case "ERROR":
      return "status-danger";
    default:
      return "status-info";
  }
};

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case "RUNNING":
      return "运行中";
    case "PAUSED":
      return "已暂停";
    case "STOPPED":
      return "已停止";
    case "ERROR":
      return "错误";
    default:
      return "未知";
  }
};

// 格式化时间
const formatTime = (timestamp: number) => {
  if (!timestamp) return "";
  return new Date(timestamp).toLocaleString("zh-CN");
};

// 重置表单数据
const resetForm = () => {
  formRef.value?.clearValidate();
};

// 加载采集状态
const loadCollectStatus = async () => {
  try {
    loading.value = true;
    const res = await getCollectStatus();
    if (res.code == 200) {
      Object.assign(form, res.data);
    } else {
      ElMessage.error(res.msg || "获取采集状态失败");
    }
  } catch (error) {
    console.error("获取采集状态失败:", error);
    ElMessage.error("获取采集状态失败，请检查网络连接");
  } finally {
    loading.value = false;
  }
};

// 打开对话框
const openDialog = async () => {
  dialogVisible.value = true;
  await loadCollectStatus();
};

// 启动采集
const handleStart = async () => {
  loading.value = true;
  try {
    const res = await http.post("/product-collect/start", form.currentConfig);
    if (res.code == 200) {
      ElMessage.success("启动采集成功");
      await loadCollectStatus();
    } else {
      ElMessage.error(res.msg || "启动采集失败");
    }
  } catch (error) {
    console.error("启动采集失败:", error);
    ElMessage.error("启动采集失败，请检查网络连接");
  } finally {
    loading.value = false;
  }
};

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return;

  const valid = await formRef.value.validate().catch(() => false);
  if (!valid) return;

  loading.value = true;

  try {
    const res = await http.put("/product-collect/config", {
      config: form.currentConfig
    });

    if (res.code == 200) {
      ElMessage.success("配置保存成功");
      await loadCollectStatus();
    } else {
      ElMessage.error(res.msg || "保存配置失败");
    }
  } catch (error) {
    console.error("保存配置失败:", error);
    ElMessage.error("保存配置失败，请检查网络连接");
  } finally {
    loading.value = false;
  }
};

defineExpose({
  openDialog
});
</script>

<style lang="scss" scoped>
.settings-container {
  padding: 0;

  // font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

// 任务状态区域
.status-section {
  margin-bottom: 20px;
}
.section-title {
  display: inline-block;
  min-width: 80px;
  padding-bottom: 8px;
  margin: 0 0 16px;
  font-size: 16px;
  color: #333333;
  border-bottom: 2px solid #409eff;
}
.status-grid {
  display: flex;
  gap: 40px;
  margin-bottom: 16px;
}
.status-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.status-label {
  font-size: 14px;
  color: #666666;
}
.status-indicator {
  display: flex;
  gap: 8px;
  align-items: center;
}
.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  &.status-success {
    background-color: #67c23a;
  }
  &.status-warning {
    background-color: #e6a23c;
  }
  &.status-info {
    background-color: #909399;
  }
  &.status-danger {
    background-color: #f56c6c;
  }
}
.status-text {
  font-size: 14px;
  color: #333333;
}
.stats-grid {
  display: flex;
  gap: 40px;
  margin-bottom: 16px;
}
.stat-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}
.stat-label {
  font-size: 14px;
  color: #666666;
}
.stat-value {
  font-size: 20px;
  color: #409eff;
}
.time-grid {
  display: flex;
  gap: 20px;
}
.time-item {
  display: flex;
  flex: 1;
  flex-direction: column;
  gap: 8px;
}
.time-label {
  font-size: 14px;
  color: #666666;
}

// 采集配置区域
.config-section {
  margin-top: 20px;
}
.config-form {
  margin-top: 16px;
  :deep(.el-form-item) {
    margin-bottom: 16px;
  }
  :deep(.el-form-item__label) {
    margin-bottom: 8px;
    font-size: 14px;
    color: #666666;
  }
}

// 底部按钮区域
.dialog-footer {
  display: flex;
  gap: 12px;
  justify-content: center;
  padding: 16px 0 0;
  margin-top: 20px;
  border-top: 1px solid #f0f0f0;
}
.cancel-btn {
  min-width: 80px;
  height: 36px;
  font-size: 14px;
  color: #666666;
  background: #ffffff;
  border: 1px solid #dddddd;
  border-radius: 4px;
  &:hover {
    color: #333333;
    border-color: #c0c4cc;
  }
}
.start-btn {
  min-width: 80px;
  height: 36px;
  font-size: 14px;
  color: white;
  background: #67c23a;
  border: 1px solid #67c23a;
  border-radius: 4px;
  &:hover {
    background: #85ce61;
    border-color: #85ce61;
  }
}
.save-btn {
  min-width: 80px;
  height: 36px;
  font-size: 14px;
  color: white;
  background: #409eff;
  border: 1px solid #409eff;
  border-radius: 4px;
  &:hover {
    background: #66b1ff;
    border-color: #66b1ff;
  }
}

// Dialog样式覆盖
:deep(.el-dialog) {
  overflow: hidden;
  border-radius: 8px;
  .el-dialog__header {
    padding: 20px 24px 0;
    background: white;
    .el-dialog__title {
      font-size: 18px;
      font-weight: 500;
      color: #333333;
    }
    .el-dialog__headerbtn {
      top: 16px;
      right: 20px;
      .el-dialog__close {
        font-size: 16px;
        color: #999999;
        &:hover {
          color: #666666;
        }
      }
    }
  }
  .el-dialog__body {
    padding: 24px;
  }
  .el-dialog__footer {
    padding: 0 24px 24px;
  }
}

// 选择器样式
:deep(.el-select) {
  .el-input__wrapper {
    border: 1px solid #dddddd;
    border-radius: 4px;
    box-shadow: none;
    &:hover {
      border-color: #c0c4cc;
    }
    &.is-focus {
      border-color: #409eff;
    }
  }
}

// 标签样式调整
:deep(.el-tag) {
  font-weight: 500;
  border: none;
  &.el-tag--success {
    color: #67c23a;
    background-color: #f0f9ff;
  }
  &.el-tag--info {
    color: #909399;
    background-color: #f4f4f5;
  }
  &.el-tag--warning {
    color: #e6a23c;
    background-color: #fdf6ec;
  }
  &.el-tag--danger {
    color: #f56c6c;
    background-color: #fef0f0;
  }
}
</style>
