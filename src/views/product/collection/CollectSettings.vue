<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      title="采集设置"
      width="600px"
      :draggable="true"
      :align-center="true"
      :destroy-on-close="true"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="resetForm"
    >
      <div class="settings-container">
        <el-form :model="form" ref="formRef" :rules="rules" label-position="top">
          <!-- 任务状态信息 -->
          <div class="status-section">
            <h3 class="section-title">任务状态</h3>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="当前状态">
                  <el-tag :type="getStatusType(form.taskStatus)" size="large" effect="dark">
                    {{ getStatusText(form.taskStatus) }}
                  </el-tag>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="运行状态">
                  <el-tag :type="form.isRunning ? 'success' : 'info'" size="large">
                    {{ form.isRunning ? "运行中" : "未运行" }}
                  </el-tag>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="已采集商品">
                  <el-statistic :value="form.collectedProductCount" suffix="个" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="新增商品">
                  <el-statistic :value="form.newProductCount" suffix="个" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="同步次数">
                  <el-statistic :value="form.syncCount" suffix="次" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="任务开始时间">
                  <el-input :value="formatTime(form.taskStartTime)" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="最后同步时间">
                  <el-input :value="formatTime(form.lastSyncTime)" disabled />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <el-divider />

          <!-- 采集配置 -->
          <div class="config-section">
            <h3 class="section-title">采集配置</h3>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="页码" prop="pn">
                  <el-input-number
                    v-model="form.currentConfig.pn"
                    :min="1"
                    :max="999"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="每页数量" prop="ps">
                  <el-input-number
                    v-model="form.currentConfig.ps"
                    :min="1"
                    :max="100"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="排序方式" prop="sort">
                  <el-select v-model="form.currentConfig.sort" style="width: 100%">
                    <el-option label="最新排序" value="newestSort" />
                    <el-option label="价格排序" value="priceSort" />
                    <el-option label="销量排序" value="salesSort" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="采集间隔(秒)" prop="interval">
                  <el-input-number
                    v-model="form.currentConfig.interval"
                    :min="10"
                    :max="3600"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="价格筛选">
                  <el-input v-model="form.currentConfig.price" placeholder="如: 100-500" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="关键词搜索">
                  <el-input v-model="form.currentConfig.q" placeholder="请输入搜索关键词" clearable />
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="当前API地址">
              <el-input v-model="form.currentApiUrl" disabled type="textarea" :autosize="{ minRows: 2, maxRows: 3 }" />
            </el-form-item>
          </div>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false" :disabled="loading">取消</el-button>
          <el-button v-if="!form.isRunning" type="success" @click="handleStart" :loading="loading">
            {{ loading ? "启动中..." : "启动采集" }}
          </el-button>
          <el-button v-if="form.isRunning && !form.isPaused" type="warning" @click="handlePause" :loading="loading">
            {{ loading ? "暂停中..." : "暂停采集" }}
          </el-button>
          <el-button v-if="form.isRunning" type="danger" @click="handleStop" :loading="loading">
            {{ loading ? "停止中..." : "停止采集" }}
          </el-button>
          <el-button type="primary" @click="submitForm" :loading="loading">
            {{ loading ? "保存中..." : "保存配置" }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from "vue";
import { ElMessage, type FormInstance } from "element-plus";
import http from "@/api";
import { getCollectStatus } from "@/views/product/interface/source";

const dialogVisible = ref(false);
const formRef = ref<FormInstance>();
const loading = ref(false);

interface CollectConfig {
  pn: number;
  ps: number;
  sort: string;
  price: string | null;
  q: string | null;
  interval: number;
}

interface CollectStatus {
  isPaused: boolean;
  isStopped: boolean;
  lastSyncTime: number;
  isRunning: boolean;
  success: boolean;
  collectedProductCount: number;
  newProductCount: number;
  syncCount: number;
  currentConfig: CollectConfig;
  currentApiUrl: string;
  taskStatus: string;
  taskStartTime: number;
}

const form = reactive<CollectStatus>({
  isPaused: false,
  isStopped: true,
  lastSyncTime: 0,
  isRunning: false,
  success: true,
  collectedProductCount: 0,
  newProductCount: 0,
  syncCount: 0,
  currentConfig: {
    pn: 1,
    ps: 20,
    sort: "newestSort",
    price: null,
    q: null,
    interval: 60
  },
  currentApiUrl: "",
  taskStatus: "STOPPED",
  taskStartTime: 0
});

// 表单验证规则
const rules = {
  "currentConfig.pn": [
    { required: true, message: "请输入页码", trigger: "blur" },
    { type: "number", min: 1, message: "页码必须大于0", trigger: "blur" }
  ],
  "currentConfig.ps": [
    { required: true, message: "请输入每页数量", trigger: "blur" },
    { type: "number", min: 1, max: 100, message: "每页数量在1-100之间", trigger: "blur" }
  ],
  "currentConfig.sort": [{ required: true, message: "请选择排序方式", trigger: "change" }],
  "currentConfig.interval": [
    { required: true, message: "请输入采集间隔", trigger: "blur" },
    { type: "number", min: 10, max: 3600, message: "采集间隔在10-3600秒之间", trigger: "blur" }
  ]
};

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case "RUNNING":
      return "success";
    case "PAUSED":
      return "warning";
    case "STOPPED":
      return "info";
    case "ERROR":
      return "danger";
    default:
      return "info";
  }
};

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case "RUNNING":
      return "运行中";
    case "PAUSED":
      return "已暂停";
    case "STOPPED":
      return "已停止";
    case "ERROR":
      return "错误";
    default:
      return "未知";
  }
};

// 格式化时间
const formatTime = (timestamp: number) => {
  if (!timestamp) return "暂无";
  return new Date(timestamp).toLocaleString("zh-CN");
};

// 重置表单数据
const resetForm = () => {
  formRef.value?.clearValidate();
};

// 加载采集状态
const loadCollectStatus = async () => {
  try {
    loading.value = true;
    const res = await getCollectStatus();
    if (res.code == 200) {
      Object.assign(form, res.data);
    } else {
      ElMessage.error(res.msg || "获取采集状态失败");
    }
  } catch (error) {
    console.error("获取采集状态失败:", error);
    ElMessage.error("获取采集状态失败，请检查网络连接");
  } finally {
    loading.value = false;
  }
};

// 打开对话框
const openDialog = async () => {
  dialogVisible.value = true;
  await loadCollectStatus();
};

// 启动采集
const handleStart = async () => {
  loading.value = true;
  try {
    const res = await http.post("/product-collect/start", form.currentConfig);
    if (res.code == 200) {
      ElMessage.success("启动采集成功");
      await loadCollectStatus();
    } else {
      ElMessage.error(res.msg || "启动采集失败");
    }
  } catch (error) {
    console.error("启动采集失败:", error);
    ElMessage.error("启动采集失败，请检查网络连接");
  } finally {
    loading.value = false;
  }
};

// 暂停采集
const handlePause = async () => {
  loading.value = true;
  try {
    const res = await http.post("/product-collect/pause");
    if (res.code == 200) {
      ElMessage.success("暂停采集成功");
      await loadCollectStatus();
    } else {
      ElMessage.error(res.msg || "暂停采集失败");
    }
  } catch (error) {
    console.error("暂停采集失败:", error);
    ElMessage.error("暂停采集失败，请检查网络连接");
  } finally {
    loading.value = false;
  }
};

// 停止采集
const handleStop = async () => {
  loading.value = true;
  try {
    const res = await http.post("/product-collect/stop");
    if (res.code == 200) {
      ElMessage.success("停止采集成功");
      await loadCollectStatus();
    } else {
      ElMessage.error(res.msg || "停止采集失败");
    }
  } catch (error) {
    console.error("停止采集失败:", error);
    ElMessage.error("停止采集失败，请检查网络连接");
  } finally {
    loading.value = false;
  }
};

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return;

  const valid = await formRef.value.validate().catch(() => false);
  if (!valid) return;

  loading.value = true;

  try {
    const res = await http.put("/product-collect/config", {
      config: form.currentConfig
    });

    if (res.code == 200) {
      ElMessage.success("配置保存成功");
      await loadCollectStatus();
    } else {
      ElMessage.error(res.msg || "保存配置失败");
    }
  } catch (error) {
    console.error("保存配置失败:", error);
    ElMessage.error("保存配置失败，请检查网络连接");
  } finally {
    loading.value = false;
  }
};

defineExpose({
  openDialog
});
</script>

<style lang="scss" scoped>
.settings-container {
  padding: 20px;
}
.section-title {
  padding-bottom: 8px;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-bottom: 2px solid #409eff;
}
.status-section {
  margin-bottom: 20px;
  .el-statistic {
    text-align: center;
    :deep(.el-statistic__content) {
      font-size: 24px;
      font-weight: 600;
      color: #409eff;
    }
  }
}
.config-section {
  .el-form-item {
    margin-bottom: 20px;
  }
}
:deep(.el-form-item__label) {
  margin-bottom: 8px;
  font-weight: 500;
  color: #606266;
}
:deep(.el-input__wrapper) {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  box-shadow: none;
  transition: border-color 0.3s;
  &:hover {
    border-color: #c0c4cc;
  }
  &.is-focus {
    border-color: #409eff;
  }
}
:deep(.el-input-number) {
  .el-input__wrapper {
    border-radius: 4px;
  }
}
:deep(.el-select) {
  .el-input__wrapper {
    border-radius: 4px;
  }
}
:deep(.el-textarea__inner) {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  box-shadow: none;
  transition: border-color 0.3s;
  &:hover {
    border-color: #c0c4cc;
  }
  &:focus {
    border-color: #409eff;
  }
}
.dialog-footer {
  display: flex;
  gap: 16px;
  justify-content: center;
  padding: 20px;
  margin: 0 -20px -20px;
  background: #fafafa;
  border-radius: 0 0 8px 8px;
  .el-button {
    min-width: 100px;
  }
}

// 全局dialog样式调整
:deep(.el-dialog) {
  overflow: hidden;
  border-radius: 12px;
  .el-dialog__header {
    padding: 20px 20px 0;
    background: white;
    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }
  .el-dialog__body {
    padding: 0;
  }
  .el-dialog__footer {
    padding: 0;
  }
}

// 状态标签样式
:deep(.el-tag) {
  font-weight: 500;
  border-radius: 4px;
}

// 统计数字样式
:deep(.el-statistic) {
  .el-statistic__head {
    margin-bottom: 4px;
    font-size: 14px;
    color: #909399;
  }
}

// 分割线样式
:deep(.el-divider) {
  margin: 24px 0;
  border-color: #e4e7ed;
}
</style>
