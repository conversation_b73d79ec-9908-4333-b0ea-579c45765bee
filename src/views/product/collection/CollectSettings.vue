<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      title="采集设置"
      width="700px"
      :draggable="true"
      :align-center="true"
      :destroy-on-close="true"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="resetForm"
    >
      <div class="settings-container">
        <!-- 任务状态区域 -->
        <div class="status-section">
          <div class="section-header">
            <h3 class="section-title">任务状态</h3>
          </div>

          <div class="status-row">
            <div class="status-item">
              <div class="status-label">当前状态</div>
              <el-tag :type="getStatusType(form.taskStatus)" size="large" class="status-tag">
                {{ getStatusText(form.taskStatus) }}
              </el-tag>
            </div>
            <div class="status-item">
              <div class="status-label">运行状态</div>
              <el-tag :type="form.isRunning ? 'success' : 'info'" size="large" class="status-tag">
                {{ form.isRunning ? "运行中" : "未运行" }}
              </el-tag>
            </div>
          </div>

          <div class="stats-row">
            <div class="stat-item">
              <div class="stat-label">已采集商品</div>
              <div class="stat-value">{{ form.collectedProductCount }}<span class="stat-unit">个</span></div>
            </div>
            <div class="stat-item">
              <div class="stat-label">新增商品</div>
              <div class="stat-value">{{ form.newProductCount }}<span class="stat-unit">个</span></div>
            </div>
            <div class="stat-item">
              <div class="stat-label">同步次数</div>
              <div class="stat-value">{{ form.syncCount }}<span class="stat-unit">次</span></div>
            </div>
          </div>

          <div class="time-row">
            <div class="time-item">
              <div class="time-label">任务开始时间</div>
              <el-input :value="formatTime(form.taskStartTime)" disabled class="time-input" placeholder="暂无" />
            </div>
            <div class="time-item">
              <div class="time-label">最后同步时间</div>
              <el-input :value="formatTime(form.lastSyncTime)" disabled class="time-input" placeholder="暂无" />
            </div>
          </div>
        </div>

        <!-- 采集配置区域 -->
        <div class="config-section">
          <div class="section-header">
            <h3 class="section-title">采集配置</h3>
          </div>

          <el-form :model="form" ref="formRef" :rules="rules" class="config-form">
            <div class="form-row">
              <div class="form-item">
                <div class="form-label">页码</div>
                <el-input-number
                  v-model="form.currentConfig.pn"
                  :min="1"
                  :max="999"
                  controls-position="right"
                  class="form-input"
                />
              </div>
              <div class="form-item">
                <div class="form-label">每页数量</div>
                <el-input-number
                  v-model="form.currentConfig.ps"
                  :min="1"
                  :max="100"
                  controls-position="right"
                  class="form-input"
                />
              </div>
            </div>

            <div class="form-row">
              <div class="form-item">
                <div class="form-label">排序方式</div>
                <el-select v-model="form.currentConfig.sort" class="form-input">
                  <el-option label="最新排序" value="newestSort" />
                  <el-option label="价格排序" value="priceSort" />
                  <el-option label="销量排序" value="salesSort" />
                </el-select>
              </div>
              <div class="form-item">
                <div class="form-label">采集间隔(秒)</div>
                <el-input-number
                  v-model="form.currentConfig.interval"
                  :min="10"
                  :max="3600"
                  controls-position="right"
                  class="form-input"
                />
              </div>
            </div>

            <div class="form-row">
              <div class="form-item">
                <div class="form-label">价格筛选</div>
                <el-input v-model="form.currentConfig.price" placeholder="如: 100-500" clearable class="form-input" />
              </div>
              <div class="form-item">
                <div class="form-label">关键词搜索</div>
                <el-input v-model="form.currentConfig.q" placeholder="请输入搜索关键词" clearable class="form-input" />
              </div>
            </div>

            <div class="form-full-row">
              <div class="form-label">当前API地址</div>
              <el-input
                v-model="form.currentApiUrl"
                disabled
                type="textarea"
                :autosize="{ minRows: 3, maxRows: 4 }"
                class="form-textarea"
              />
            </div>
          </el-form>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false" :disabled="loading" class="cancel-btn">取消</el-button>
          <el-button v-if="!form.isRunning" type="success" @click="handleStart" :loading="loading" class="start-btn">
            启动采集
          </el-button>
          <el-button type="primary" @click="submitForm" :loading="loading" class="save-btn"> 保存配置 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from "vue";
import { ElMessage, type FormInstance } from "element-plus";
import http from "@/api";
import { getCollectStatus } from "@/views/product/interface/source";

const dialogVisible = ref(false);
const formRef = ref<FormInstance>();
const loading = ref(false);

interface CollectConfig {
  pn: number;
  ps: number;
  sort: string;
  price: string | null;
  q: string | null;
  interval: number;
}

interface CollectStatus {
  isPaused: boolean;
  isStopped: boolean;
  lastSyncTime: number;
  isRunning: boolean;
  success: boolean;
  collectedProductCount: number;
  newProductCount: number;
  syncCount: number;
  currentConfig: CollectConfig;
  currentApiUrl: string;
  taskStatus: string;
  taskStartTime: number;
}

const form = reactive<CollectStatus>({
  isPaused: false,
  isStopped: true,
  lastSyncTime: 0,
  isRunning: false,
  success: true,
  collectedProductCount: 0,
  newProductCount: 0,
  syncCount: 0,
  currentConfig: {
    pn: 1,
    ps: 20,
    sort: "newestSort",
    price: null,
    q: null,
    interval: 60
  },
  currentApiUrl: "",
  taskStatus: "STOPPED",
  taskStartTime: 0
});

// 表单验证规则
const rules = {
  "currentConfig.pn": [
    { required: true, message: "请输入页码", trigger: "blur" },
    { type: "number", min: 1, message: "页码必须大于0", trigger: "blur" }
  ],
  "currentConfig.ps": [
    { required: true, message: "请输入每页数量", trigger: "blur" },
    { type: "number", min: 1, max: 100, message: "每页数量在1-100之间", trigger: "blur" }
  ],
  "currentConfig.sort": [{ required: true, message: "请选择排序方式", trigger: "change" }],
  "currentConfig.interval": [
    { required: true, message: "请输入采集间隔", trigger: "blur" },
    { type: "number", min: 10, max: 3600, message: "采集间隔在10-3600秒之间", trigger: "blur" }
  ]
};

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case "RUNNING":
      return "success";
    case "PAUSED":
      return "warning";
    case "STOPPED":
      return "info";
    case "ERROR":
      return "danger";
    default:
      return "info";
  }
};

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case "RUNNING":
      return "运行中";
    case "PAUSED":
      return "已暂停";
    case "STOPPED":
      return "已停止";
    case "ERROR":
      return "错误";
    default:
      return "未知";
  }
};

// 格式化时间
const formatTime = (timestamp: number) => {
  if (!timestamp) return "";
  return new Date(timestamp).toLocaleString("zh-CN");
};

// 重置表单数据
const resetForm = () => {
  formRef.value?.clearValidate();
};

// 加载采集状态
const loadCollectStatus = async () => {
  try {
    loading.value = true;
    const res = await getCollectStatus();
    if (res.code == 200) {
      Object.assign(form, res.data);
    } else {
      ElMessage.error(res.msg || "获取采集状态失败");
    }
  } catch (error) {
    console.error("获取采集状态失败:", error);
    ElMessage.error("获取采集状态失败，请检查网络连接");
  } finally {
    loading.value = false;
  }
};

// 打开对话框
const openDialog = async () => {
  dialogVisible.value = true;
  await loadCollectStatus();
};

// 启动采集
const handleStart = async () => {
  loading.value = true;
  try {
    const res = await http.post("/product-collect/start", form.currentConfig);
    if (res.code == 200) {
      ElMessage.success("启动采集成功");
      await loadCollectStatus();
    } else {
      ElMessage.error(res.msg || "启动采集失败");
    }
  } catch (error) {
    console.error("启动采集失败:", error);
    ElMessage.error("启动采集失败，请检查网络连接");
  } finally {
    loading.value = false;
  }
};

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return;

  const valid = await formRef.value.validate().catch(() => false);
  if (!valid) return;

  loading.value = true;

  try {
    const res = await http.put("/product-collect/config", {
      config: form.currentConfig
    });

    if (res.code == 200) {
      ElMessage.success("配置保存成功");
      await loadCollectStatus();
    } else {
      ElMessage.error(res.msg || "保存配置失败");
    }
  } catch (error) {
    console.error("保存配置失败:", error);
    ElMessage.error("保存配置失败，请检查网络连接");
  } finally {
    loading.value = false;
  }
};

defineExpose({
  openDialog
});
</script>

<style lang="scss" scoped>
.settings-container {
  padding: 0;

  // font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

// 任务状态区域
.status-section {
  margin-bottom: 32px;
}
.section-header {
  margin-bottom: 20px;
}
.section-title {
  display: inline-block;
  min-width: 80px;
  padding-bottom: 8px;
  margin: 0;
  font-size: 16px;

  // font-weight: 500;
  color: #333333;
  border-bottom: 2px solid #409eff;
}
.status-row {
  display: flex;
  gap: 40px;
  margin-bottom: 24px;
}
.status-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.status-label {
  font-size: 14px;
  font-weight: 400;
  color: #666666;
}
.status-tag {
  padding: 8px 16px;
  font-weight: 500;
  border-radius: 4px;
}
.stats-row {
  display: flex;
  gap: 60px;
  margin-bottom: 24px;
}
.stat-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.stat-label {
  font-size: 14px;
  font-weight: 400;
  color: #666666;
}
.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #409eff;
  .stat-unit {
    margin-left: 2px;
    font-size: 14px;
    font-weight: 400;
    color: #666666;
  }
}
.time-row {
  display: flex;
  gap: 40px;
}
.time-item {
  display: flex;
  flex: 1;
  flex-direction: column;
  gap: 8px;
}
.time-label {
  font-size: 14px;
  font-weight: 400;
  color: #666666;
}
.time-input {
  :deep(.el-input__wrapper) {
    background-color: #f5f5f5;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    box-shadow: none;
    .el-input__inner {
      font-size: 14px;
      color: #999999;
    }
  }
}

// 采集配置区域
.config-section {
  margin-top: 32px;
}
.config-form {
  margin-top: 20px;
}
.form-row {
  display: flex;
  gap: 40px;
  margin-bottom: 20px;
}
.form-item {
  display: flex;
  flex: 1;
  flex-direction: column;
  gap: 8px;
}
.form-label {
  font-size: 14px;
  font-weight: 400;
  color: #666666;
}
.form-input {
  width: 100%;
  :deep(.el-input__wrapper) {
    border: 1px solid #dddddd;
    border-radius: 4px;
    box-shadow: none;
    transition: border-color 0.2s;
    &:hover {
      border-color: #c0c4cc;
    }
    &.is-focus {
      border-color: #409eff;
    }
  }
  :deep(.el-input-number__decrease),
  :deep(.el-input-number__increase) {
    color: #999999;
    background: transparent;
    border: none;
    &:hover {
      color: #409eff;
    }
  }
}
.form-full-row {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 20px;
}
.form-textarea {
  :deep(.el-textarea__inner) {
    font-size: 14px;
    color: #999999;
    resize: none;
    background-color: #f5f5f5;
    border: 1px solid #dddddd;
    border-radius: 4px;
  }
}

// 底部按钮区域
.dialog-footer {
  display: flex;
  gap: 12px;
  justify-content: center;
  padding: 20px 0 0;
  margin-top: 32px;
  border-top: 1px solid #f0f0f0;
}
.cancel-btn {
  min-width: 80px;
  height: 36px;
  font-size: 14px;
  color: #666666;
  background: #ffffff;
  border: 1px solid #dddddd;
  border-radius: 4px;
  &:hover {
    color: #333333;
    border-color: #c0c4cc;
  }
}
.start-btn {
  min-width: 80px;
  height: 36px;
  font-size: 14px;
  color: white;
  background: #67c23a;
  border: 1px solid #67c23a;
  border-radius: 4px;
  &:hover {
    background: #85ce61;
    border-color: #85ce61;
  }
}
.save-btn {
  min-width: 80px;
  height: 36px;
  font-size: 14px;
  color: white;
  background: #409eff;
  border: 1px solid #409eff;
  border-radius: 4px;
  &:hover {
    background: #66b1ff;
    border-color: #66b1ff;
  }
}

// Dialog样式覆盖
:deep(.el-dialog) {
  overflow: hidden;
  border-radius: 8px;
  .el-dialog__header {
    padding: 20px 24px 0;
    background: white;
    .el-dialog__title {
      font-size: 18px;
      font-weight: 500;
      color: #333333;
    }
    .el-dialog__headerbtn {
      top: 16px;
      right: 20px;
      .el-dialog__close {
        font-size: 16px;
        color: #999999;
        &:hover {
          color: #666666;
        }
      }
    }
  }
  .el-dialog__body {
    padding: 24px;
  }
  .el-dialog__footer {
    padding: 0 24px 24px;
  }
}

// 选择器样式
:deep(.el-select) {
  .el-input__wrapper {
    border: 1px solid #dddddd;
    border-radius: 4px;
    box-shadow: none;
    &:hover {
      border-color: #c0c4cc;
    }
    &.is-focus {
      border-color: #409eff;
    }
  }
}

// 标签样式调整
:deep(.el-tag) {
  font-weight: 500;
  border: none;
  &.el-tag--success {
    color: #67c23a;
    background-color: #f0f9ff;
  }
  &.el-tag--info {
    color: #909399;
    background-color: #f4f4f5;
  }
  &.el-tag--warning {
    color: #e6a23c;
    background-color: #fdf6ec;
  }
  &.el-tag--danger {
    color: #f56c6c;
    background-color: #fef0f0;
  }
}
</style>

1. 不能使用自定义字体
2. 高度