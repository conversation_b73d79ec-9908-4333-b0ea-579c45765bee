<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      :columns="columns"
      :request-api="getProductCollectionList"
      :search-col="6"
      highlight-current-row
      :tool-button="true"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button type="primary" @click="openDialogTasks('平台账号', 'go2.login')">平台账号</el-button>
        <el-button type="primary" @click="openCollectSettings">采集设置</el-button>
        <el-button type="danger" @click="handleDeleteAll"> 一键清空 </el-button>
      </template>
      <!-- 表格操作 -->
      <template #operation="scope">
        <el-button type="primary" link @click="handleProductIn(scope.row)">入库</el-button>
        <el-button type="primary" link @click="handleDownloadImage(scope.row.pid)">下载原图</el-button>
        <el-button type="primary" link @click="handleSourceDetail('image', scope.row.pid)">主图</el-button>
        <el-button type="primary" link @click="handleSourceDetail('detail', scope.row.pid)">详情</el-button>
        <el-button type="danger" link @click="handleDelete(scope.row)">删除</el-button>
        <!-- <a class="table-button" @click="handleProductIn(scope.row)">入库</a>
        <a class="table-button" @click="handleDownloadImage(scope.row.pid)">下载</a>
        <a class="table-button" @click="handleSourceDetail('image', scope.row.pid)">主图</a>
        <a class="table-button" @click="handleSourceDetail('detail', scope.row.pid)">详情</a>
        <a class="table-button" @click="handleDelete(scope.row)">删除</a> -->
      </template>
    </ProTable>

    <!-- 入库 -->
    <ProductIn ref="productInRef" />
    <!-- 货源详情 -->
    <SourceDetail ref="sourceDetailRef" />
    <!-- 下载图片 -->
    <DownloadImage ref="downloadImageRef" />
    <!-- 任务设置 -->
    <Tasks ref="tasksRef" />
    <!-- 采集设置 -->
    <CollectSettings ref="collectSettingsRef" />
  </div>
</template>
<script lang="tsx" setup name="productCollection">
import { ref, reactive } from "vue";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { ElMessage, ElMessageBox } from "element-plus";
import { Product } from "@/views/product/interface/index";
import { getProductCollectionList, deleteProductCollection } from "@/views/product/interface/source";
import ProductIn from "@/views/product/components/ProductIn.vue";
import SourceDetail from "@/views/product/components/Detail.vue";
import DownloadImage from "@/views/product/components/Download.vue";
import Tasks from "@/views/components/tasks/Tasks.vue";
import CollectSettings from "./CollectSettings.vue";
const tasksRef = ref();
const collectSettingsRef = ref<InstanceType<typeof CollectSettings> | null>(null);

const openDialogTasks = (name: string, method: string) => {
  tasksRef.value.acceptParams(name, method);
};

const openCollectSettings = () => {
  collectSettingsRef.value?.openDialog();
};
const downloadImageRef = ref<InstanceType<typeof DownloadImage> | null>(null);
const sourceDetailRef = ref<InstanceType<typeof SourceDetail> | null>(null);
const productInRef = ref<InstanceType<typeof ProductIn> | null>(null);
const proTable = ref<ProTableInstance>();
const columns = reactive<ColumnProps<Product.Collection>[]>([
  { type: "selection", fixed: "left" },
  { type: "index", label: "序号", width: 70 },
  { prop: "brand", label: "名称" },
  {
    prop: "articleNumber",
    label: "档口货号",
    render: scope => {
      return (
        <a
          class="table-button"
          onClick={() => {
            window.open(`https://www.go2.cn/product/${scope.row.pid}.html`);
          }}
        >
          {scope.row.articleNumber}
        </a>
      );
    }
  },
  { prop: "price", label: "价格" },
  {
    prop: "size",
    label: "尺码",
    render: scope => {
      return scope.row.size.join("/");
    }
  },
  { prop: "createTime", label: "采集时间" },
  { prop: "operation", label: "操作", fixed: "right", width: 280 }
]);
// 辅助函数用于生成时间范围
const getDateRange = (days: number) => {
  const end = new Date(); // 当前时间
  const start = new Date();
  start.setDate(start.getDate() - days);
  return [start, end];
};
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const shortcuts = [
  {
    text: "今天",
    value: () => getDateRange(0) // 显示今天
  },
  {
    text: "昨天",
    value: () => getDateRange(1) // 显示昨天
  },
  {
    text: "上周",
    value: () => getDateRange(7) // 显示过去一周
  }
];

// 删除待入库商品
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const handleDelete = (row: Product.Collection) => {
  ElMessageBox.confirm("确定删除该商品吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(() => {
      deleteProductCollection(row.id).then(() => {
        proTable.value?.getTableList();
        ElMessage({
          type: "success",
          message: "删除成功"
        });
      });
    })
    .catch(() => {
      ElMessage({});
    });
};
// 删除全部数据
const handleDeleteAll = () => {
  ElMessageBox.confirm("确定要删除全部数据吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    // deleteAll().then(() => {
    //   ElMessage.success("删除成功");
    //   proTable.value?.getTableList();
    // });
  });
};
// 添加入库
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const handleProductIn = async (row: Product.Collection) => {
  const form = {
    lastCatetory: [], // 选择的分类结果
    category: [], // [11729, 11731, 9777]
    path: [], // ['鞋靴', '时尚女鞋', '雨鞋/雨靴']
    propsOptions: [], // 产品属性列表
    props: {}
  };
  const rules: { [key: string]: any } = {
    lastCatetory: [{ required: true, message: "请选择分类", trigger: "change" }],
    props: {}
  };
  const params: { [key: string]: any } = {
    row: row,
    form: JSON.parse(JSON.stringify(form)),
    rules: JSON.parse(JSON.stringify(rules)),
    getTableList: proTable.value?.getTableList
  };

  productInRef.value?.acceptParams(params);
};
// 货源详情
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const handleSourceDetail = (action: string, pid: string) => {
  sourceDetailRef.value?.acceptParams(action, pid);
};
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const handleDownloadImage = (pid: string) => {
  downloadImageRef.value?.acceptParams(pid);
};
</script>
<style>
.el-row {
  margin-bottom: 20px;
}
.el-row:last-child {
  margin-bottom: 0;
}
.el-col {
  border-radius: 4px;
}
.grid-content {
  min-height: 36px;
  background-color: #ffffff;
  border-radius: 4px;
}
.card_box {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  height: 80px;
  border-radius: 4px;
  -webkit-box-align: center;
  .card_box_cir {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    -webkit-box-pack: center;
    -webkit-box-align: center;
    .size {
      margin-right: 20px;
      margin-left: 20px;
      font-size: 45px;
    }
  }
  .card_box_txt {
    .sp1 {
      display: block;
      font-size: 24px;
      color: rgb(37 38 49);
    }
    .sp2 {
      display: block;
      font-size: 12px;
      color: rgb(152 169 188);
    }
  }
}
</style>
