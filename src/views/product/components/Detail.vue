<template>
  <div>
    <div v-if="dialogAction === 'image'">
      <el-image-viewer v-if="showViewer" @close="close" :url-list="imageUrlList" />
    </div>
    <el-dialog
      title="货源详情"
      class="card"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      :align-center="true"
      v-model="dialogVisible"
      @close="dialogClose"
    >
      <div v-if="dialogAction === 'detail'" style="height: 700px; overflow: auto; text-align: center">
        <el-descriptions :column="1" size="small" border style="margin-bottom: 20px">
          <el-descriptions-item v-for="item in sourceDetail.props.split(',')" :key="item" width="120" :label="item.split(':')[0]">
            {{ item.split(":")[1] }}
          </el-descriptions-item>
        </el-descriptions>
        <div v-html="sourceDetail.desc"></div>
      </div>
      <template #footer>
        <div class="dialog-footer" style="text-align: center">
          <!-- <el-button type="primary"> 下载原图{{ currentPid }} </el-button> -->
          <el-button type="primary" @click="handleDownloadImage(currentPid)">下载原图</el-button>
          <el-button @click="dialogVisible = false">关闭</el-button>
        </div>
      </template>
      <!-- 按钮部分 -->
    </el-dialog>
    <!-- 下载图片 -->
    <DownloadImage ref="downloadImageRef" />
  </div>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import { getProductSourceDetail } from "@/views/product/interface/detail";
import DownloadImage from "@/views/product/components/Download.vue";
const downloadImageRef = ref<InstanceType<typeof DownloadImage> | null>(null);
const handleDownloadImage = (pid: string) => {
  downloadImageRef.value?.acceptParams(pid);
};
const dialogVisible = ref(false);
const showViewer = ref(false);
const imageUrlList = ref([]);
const dialogAction = ref();
const active = ref("deviceList");
const sourceDetail: { [key: string]: any } = ref({});
const currentPid = ref();
const acceptParams = (action: string, pid: string) => {
  currentPid.value = pid;
  getProductSourceDetail({ action: action, pid: pid }).then(res => {
    dialogAction.value = action;
    sourceDetail.value = res.data;
    if (action === "image") {
      imageUrlList.value = res.data.images;
      showViewer.value = true;
    } else if (action === "detail") {
      dialogVisible.value = true;
    }
  });
};
const close = () => {
  showViewer.value = false;
  imageUrlList.value = [];
};
const dialogClose = () => {
  dialogVisible.value = false;
  active.value = "desc";
};
defineExpose({
  acceptParams
});
</script>
