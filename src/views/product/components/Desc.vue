<template>
  <div>
    <div v-if="detail.props && typeof detail.props === 'string'">
      <el-descriptions title="产品描述" :column="2" size="small" border style="margin-bottom: 20px" class="mb10">
        <el-descriptions-item v-for="item in detail.props.split(',')" :key="item" width="120" :label="item.split(':')[0]">
          {{ item.split(":")[1] }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <div v-html="detail.desc" style="margin-top: 30px" />
  </div>
</template>
<script setup lang="ts">
import { getProductSourceDetail } from "@/views/product/interface/detail";
import { ElMessage } from "element-plus";
import { onMounted, ref } from "vue";
const props = defineProps<{
  pid: string;
}>();
const detail = ref<{ [key: string]: any }>({});
onMounted(() => {
  try {
    getProductSourceDetail({ action: "detail", pid: props.pid }).then(res => {
      detail.value = res.data;
    });
  } catch (error) {
    ElMessage.error("获取商品详情失败");
    return;
  }
});
</script>
