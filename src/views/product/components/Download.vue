<template>
  <div>
    <el-dialog class="card" v-model="dialogVisible" title="图片验证码" width="1200" @close="dialogVisible = false">
      <div :style="{ backgroundImage: `url(${downloadProductInfo.image})` }" class="background-div">
        <div style="position: absolute; top: 148px; width: 100%; height: 138px">
          <div
            v-for="(area, index) in clickAreas"
            :key="index"
            :style="{
              top: area.top,
              left: area.left,
              width: area.width,
              height: area.height
            }"
            class="clickable-area"
            @click="handleDownloadImageByCode(downloadProductInfo.pid, index)"
          ></div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import { ElMessage } from "element-plus";
import { downloadProductImage, downloadProductImageByCode } from "@/views/product/interface/detail";
const dialogVisible = ref(false);
const downloadProductInfo = ref<{ [key: string]: any }>({
  image: "",
  pid: ""
});
const clickAreas = ref([
  { top: "0", left: "0", width: "165px", height: "140px" },
  { top: "0", left: "200px", width: "165px", height: "140px" },
  { top: "0", left: "395px", width: "165px", height: "140px" },
  { top: "0", left: "595px", width: "165px", height: "140px" },
  { top: "0", left: "795px", width: "165px", height: "140px" },
  { top: "0", left: "995px", width: "165px", height: "140px" }
]);
const acceptParams = async (pid: string) => {
  downloadProductImage(pid).then(res => {
    if (res.msg == "获取成功") {
      ElMessage.success("下载链接获取成功");
      const downloadUrl = res.data.url;
      window.open(downloadUrl, "_blank");
    } else {
      downloadProductInfo.value.image = "data:image/jpeg;base64," + res.data;
      downloadProductInfo.value.pid = pid;
      dialogVisible.value = true;
    }
  });
};

const handleDownloadImageByCode = async (pid: string, index: number) => {
  downloadProductImageByCode(pid, index).then(res => {
    if (res.msg === "获取成功") {
      ElMessage.success("验证成功");
      dialogVisible.value = false;
      const downloadUrl = res.data.url;
      window.open(downloadUrl, "_blank");
    } else {
      ElMessage.error(res.msg);
    }
  });
};
defineExpose({
  acceptParams
});
</script>
<style scoped>
.background-div {
  width: 100%;
  height: 200px;
  background-repeat: no-repeat;
  background-size: cover;
}
.clickable-area {
  position: absolute;
  cursor: pointer;
}
</style>
