<template>
  <div>
    <el-dialog
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      :align-center="true"
      v-model="dialogVisible"
      class="card"
      title="商品入库设置"
      width="75%"
      @close="resetParams"
    >
      <div style="display: flex; height: 700px">
        <div style="width: 750px; text-align: center" class="card">
          <Desc v-if="dialogVisible" :pid="dialogProps.row.pid" />
        </div>
        <div style="width: calc(100% - 750px)" class="card ml10 in">
          <el-form
            :model="dialogProps.form"
            ref="formRef"
            :rules="dialogProps.rules"
            label-width="120"
            label-suffix=":"
            :validate-on-rule-change="false"
            class="fm"
          >
            <el-divider content-position="left">类目属性设置</el-divider>
            <el-form-item label="选择分类" prop="options" style="margin: 20px 0">
              <el-cascader
                :options="JdCategory"
                v-model="dialogProps.form.options"
                placeholder="请选择分类"
                @change="handleManualChanged"
              ></el-cascader>
              <el-button
                class="ml10"
                type="primary"
                :disabled="dialogProps.form.ocrStatus"
                :plain="dialogProps.form.ocrStatus"
                @click="handleAutoOcrCategory"
                >自动识别
              </el-button>
            </el-form-item>
            <div>
              <!-- <el-divider content-position="left">自动识别类目</el-divider> -->
              <el-radio-group v-model="dialogProps.form.auto" @change="handleOcrCategoryChanged">
                <el-form-item
                  v-for="(item, index) in dialogProps.form.ocrCategory"
                  :key="index"
                  :label="`类目: ${Number(index) + 1}`"
                >
                  <div style="display: flex">
                    <div>
                      <span style="height: 32px; line-height: 32px">{{ item.categoryNames.join(" > ") }}</span>
                    </div>
                    <div style="margin-left: 20px">
                      <el-radio :value="index" :label="index">选取</el-radio>
                    </div>
                  </div>
                </el-form-item>
              </el-radio-group>
            </div>
            <div v-if="categoryPrice.length > 0">
              <el-divider content-position="left">类目最高限价</el-divider>
              <el-table :data="categoryPrice" style="width: 100%">
                <el-table-column prop="shopName" label="店铺名称" />
                <el-table-column label="产品价" width="100">{{ dialogProps.row.price }}</el-table-column>
                <el-table-column prop="maxPrice" label="限价" width="100" />
                <el-table-column label="毛利率" width="100">
                  <template #default="scope">
                    <el-tag>{{ handleCalcMargin(scope.row.maxPrice) }}</el-tag>
                  </template>
                </el-table-column>
              </el-table>
              <div style="margin-top: 30px; text-align: center" v-if="!dialogProps.form.insertion">
                <el-button type="primary" @click="handleContinueIn">继续入库</el-button>
                <el-button @click="dialogVisible = false">取消入库</el-button>
              </div>
            </div>
            <!-- 商品属性 -->
            <div v-if="dialogProps.form.props.productProps.length > 0">
              <el-divider content-position="left">商品属性</el-divider>
              <!-- <div v-for="(item, index) in dialogProps.form.props.productProps" :key="index" style="display: none">
                <div v-if="!['颜色', '尺码'].includes(item.name)">
                  <el-form-item :label="item.name" :key="index">
                    <el-select
                      v-if="[1, 2].includes(item.inputType)"
                      :multiple="item.inputType === 2"
                      :multiple-limit="handleMaxLimit(item)"
                      :placeholder="item.inputType === 2 ? '最多可选择' + handleMaxLimit(item) + '项' : '请选择'"
                      v-model="dialogProps.form.productProps[String(item.id)]"
                    >
                      <el-option v-for="value in item.values" :key="value.id" :label="value.name" :value="value.id" />
                    </el-select>
                    <el-text class="ml10">{{ item.inputType === 2 ? "最多可选择 " + handleMaxLimit(item) + " 项" : "" }}</el-text>
                  </el-form-item>
                </div>
              </div> -->
              <div>
                <div v-for="(item, index) in dialogProps.form.props.productProps" :key="index">
                  <el-form-item
                    v-for="(child, _index) in item.children"
                    :key="_index"
                    :label="child.title"
                    :prop="`productProps.${String(child.componentId)}`"
                  >
                    <el-select
                      v-if="['selectSingle', 'selectMulti'].includes(child.type)"
                      :multiple="child.type === 'selectMulti'"
                      :multiple-limit="child.validate[0].max"
                      :placeholder="
                        child.type === 'selectMulti'
                          ? '最多可选择' +
                            (child.validate[0].max === undefined ? child.props.options.length : child.validate[0].max) +
                            '项'
                          : '请选择'
                      "
                      v-model="dialogProps.form.productProps[String(child.componentId)]"
                    >
                      <el-option
                        v-for="options in child.props.options"
                        :key="options.value"
                        :label="options.label"
                        :value="options.value"
                      />
                    </el-select>
                    <el-cascader
                      v-model="dialogProps.form.productProps[String(child.componentId)]"
                      v-if="child.type === 'layered'"
                      :options="child.props.options"
                      :props="optionProps"
                    />
                    <el-text class="ml10">{{
                      child.type === "selectMulti"
                        ? "最多可选择 " +
                          (child.validate[0].max === undefined ? child.props.options.length : child.validate[0].max) +
                          " 项"
                        : ""
                    }}</el-text>
                  </el-form-item>
                </div>
              </div>
            </div>
            <!-- SKU必填属性 -->
            <div v-if="dialogProps.form.props.skuProps.length > 0">
              <el-divider content-position="left">SKU必填属性</el-divider>
              <div v-for="(item, index) in dialogProps.form.props.skuProps" :key="index">
                <div v-for="(child, _index) in item.childWebComponentList" :key="_index">
                  <el-form-item :key="_index" :label="child.componentName" :prop="`skuProps[${String(child.componentCode)}]`">
                    <el-select v-model="dialogProps.form.skuProps[String(child.componentCode)]">
                      <el-option
                        v-for="options in child.optionValueList"
                        :key="options.value"
                        :label="options.label"
                        :value="options.value"
                      />
                    </el-select>
                  </el-form-item>
                </div>
              </div>
            </div>
          </el-form>
        </div>
      </div>
      <!-- <div style="margin-top: 40px; text-align: center" v-if="dialogProps.form.insertion"> -->
      <div style="margin-top: 40px; text-align: center">
        <el-button type="primary" @click="handleDownloadImage(dialogProps.row.pid)">下载图片</el-button>
        <el-button type="primary" @click="submitForm(formRef)"> 入库 </el-button>
        <el-button @click="resetForm(formRef)">重置</el-button>
      </div>
    </el-dialog>
    <!-- 下载图片 -->
    <DownloadImage ref="downloadImageRef" />
  </div>
</template>
<script lang="ts" setup>
import { reactive, ref } from "vue";
import { ElMessage, type FormInstance } from "element-plus";
import http from "@/api";
import { JdCategory, findLabels, findAttrsByCategoryIdUnlimitCate } from "@/views/product/interface/category";
import { handleProductIn } from "@/views/product/interface/source";
import Desc from "@/views/product/components/Desc.vue";
const formRef = ref<FormInstance>();
const dialogVisible = ref(false);
const dialogProps = ref<{ [key: string]: any }>({});
import DownloadImage from "@/views/product/components/Download.vue";
const downloadImageRef = ref<InstanceType<typeof DownloadImage> | null>(null);
const handleDownloadImage = (pid: string) => {
  downloadImageRef.value?.acceptParams(pid);
};
const acceptParams = async (row: { [key: string]: any }) => {
  dialogProps.value = row;
  dialogVisible.value = true;
  findAttrsByCategoryIdUnlimitCate({ categoryId: 31802 }).then(res => {
    console.log(res);
  });
};
interface CategoryPrice {
  shopId: number;
  shopName: string;
  minPrice: string;
  maxPrice: string;
}
const categoryPrice = reactive<CategoryPrice[]>([]);

const handleCalcMargin = (maxPrice: string) => {
  if (Number(maxPrice) == 0) return "0%";
  // 成本价
  const price = Number(dialogProps.value.row.price);
  // 毛利
  const maoli = Number(maxPrice) * 0.92 - price;
  // 扣点后价格
  const priceAfterDeduction = Number(maxPrice) * 0.92;
  // 毛利率
  const margin = (maoli / priceAfterDeduction) * 100;
  // 保留小数点后2位
  return margin.toFixed(2) + "%";
};

defineExpose({ acceptParams });

const optionProps = { value: "value", label: "label", children: "subOptions" };

const resetParams = () => {
  // 清空dialogProps.value.form.maxPrice
  dialogProps.value.form.maxPrice = {};
  dialogProps.value.form.productProps = {};
  dialogProps.value.form.skuProps = {};
  dialogProps.value.form.props = {
    productProps: {},
    skuProps: {}
  };
  dialogProps.value.rules.productProps = {};
  dialogProps.value.rules.skuProps = {};
  categoryPrice.splice(0, categoryPrice.length);
};

// 手动选择
const handleManualChanged = (value: any) => {
  dialogProps.value.form.category = [11729, 11731, ...value];
  dialogProps.value.form.path = ["鞋靴", "时尚女鞋", ...findLabels(JdCategory, dialogProps.value.form.category)];
  resetParams();
  http
    .post<CategoryPrice[]>("/product/insertion/category/price", {
      category: dialogProps.value.form.category
    })
    .then(res => {
      categoryPrice.splice(0, categoryPrice.length);
      // categoryPrice.push(res.data);
      categoryPrice.push(...res.data);
    });
};
// 自动识别
const handleAutoOcrCategory = () => {
  dialogProps.value.form.options = [];
  resetParams();
  http.post<{ [key: string]: any }>("/product/insertion/category/by-image", { pid: dialogProps.value.row.pid }).then(res => {
    dialogProps.value.form.ocrCategory = res.data;
    if (res.msg == "识别成功") dialogProps.value.form.ocrStatus = true;
  });
};

// 选择自动识别结果
const handleOcrCategoryChanged = (index: any) => {
  const categoryIds = dialogProps.value.form.ocrCategory[Number(index)].categoryIds.map((item: string) => Number(item));
  dialogProps.value.form.options = categoryIds;
  handleManualChanged(categoryIds);
};

// 继续入库
const handleContinueIn = () => {
  const shopId = categoryPrice.find(item => Number(item.maxPrice) > 0)?.shopId;
  http
    .post<{ [key: string]: any }>("/product/insertion/category/props", {
      category: dialogProps.value.form.category,
      shopId: shopId
    })
    .then(res => {
      dialogProps.value.form.props = res.data;
      // 动态生成校验规则
      res.data.productProps.forEach((item: { children: any[] }) => {
        item.children.forEach((child: any) => {
          dialogProps.value.rules.productProps[`${String(child.componentId)}`] = [
            { required: true, message: "请选择" + child.title, trigger: "change" }
          ];
        });
      });
      res.data.skuProps.forEach((item: { childWebComponentList: any[] }) => {
        item.childWebComponentList.forEach((child: any) => {
          dialogProps.value.rules.skuProps[`${String(child.componentCode)}`] = [
            { required: true, message: "请选择" + child.componentName, trigger: "change" }
          ];
        });
      });
      dialogProps.value.form.insertion = true;
    });
};

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
};

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate((valid, fields) => {
    if (valid) {
      const params: { [key: string]: any } = {
        pid: dialogProps.value.row.pid,
        id: dialogProps.value.row.id,
        category: dialogProps.value.form.category,
        path: dialogProps.value.form.path,
        // maxPrice: dialogProps.value.form.maxPrice.maxPrice,
        productProps: dialogProps.value.form.productProps,
        skuProps: dialogProps.value.form.skuProps
      };
      handleProductIn(params).then(() => {
        ElMessage.success("入库成功");
        dialogVisible.value = false;
        dialogProps.value.getTableList();
      });
    } else {
      console.log("error submit!", fields);
    }
  });
};
// 获取多选个数
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const handleMaxLimit = (item: any) => {
  if (item.inputType === 2) {
    item.features.forEach((feature: any) => {
      if (feature.key === "valCount") {
        return feature.value;
      }
    });
  }
  return 0;
};
</script>
<style scoped>
.el-select {
  width: 50%;
}
.el-radio {
  width: 100%;
  margin-bottom: 10px;
}
.props :deep() .el-radio__label {
  width: 100% !important;
}
.props-category :deep() .el-cascader {
  width: 60%;
}
.dcalc {
  width: calc(100% - 750px);
}
.fm {
  margin: 30px 0;
  :deep() .el-cascader,
  .el-select {
    width: 60% !important;
  }
}
.in :deep() .el-input {
  height: 32px !important;
  line-height: 32px !important;
}
</style>
