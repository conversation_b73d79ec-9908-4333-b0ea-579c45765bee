<template>
  <div>
    <el-dialog
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      :align-center="true"
      v-model="dialogVisible"
      class="card"
      title="商品入库设置"
      width="75%"
      @close="resetParams"
    >
      <div style="display: flex; height: 700px">
        <div style="width: 750px; text-align: center" class="card">
          <Desc v-if="dialogVisible" :pid="dialogProps.row.pid" />
        </div>
        <div style="width: calc(100% - 750px)" class="card ml10 in">
          <el-form
            :model="dialogProps.form"
            ref="formRef"
            :rules="dialogProps.rules"
            label-width="120"
            label-suffix=":"
            :validate-on-rule-change="false"
            class="fm"
          >
            <el-divider content-position="left">类目属性设置</el-divider>
            <el-form-item label="选择分类" prop="lastCatetory" style="margin: 20px 0">
              <el-cascader
                :options="JdCategory"
                v-model="dialogProps.form.lastCatetory"
                placeholder="请选择分类"
                @change="handleChoseCategory"
              ></el-cascader>
            </el-form-item>
            <el-divider v-if="dialogProps.form.propsOptions.length > 0" content-position="left">商品属性</el-divider>
            <div v-for="item in dialogProps.form.propsOptions" :key="item.id">
              <!-- 
              inputType:
                1: 单选
                2: 多选
                3: 输入框
              -->
              <el-form-item :label="`${item.name} ` + item.inputType" :prop="`props.${String(item.id)}`">
                <!-- 多选 -->
                <el-select
                  v-if="item.inputType === 2"
                  v-model="dialogProps.form.props[item.id]"
                  multiple
                  :multiple-limit="getValCount(item.features)"
                  :placeholder="'最多可选 ' + getValCount(item.features) + ' 项'"
                >
                  <el-option v-for="options in item.value" :key="options.id" :label="options.name" :value="options.id" />
                </el-select>
                <!-- 默认单选 -->
                <el-select v-else v-model="dialogProps.form.props[item.id]">
                  <el-option v-for="options in item.value" :key="options.id" :label="options.name" :value="options.id" />
                </el-select>
              </el-form-item>
            </div>
          </el-form>
        </div>
      </div>
      <div style="margin-top: 40px; text-align: center">
        <el-button type="primary" @click="submitForm(formRef)"> 入库 </el-button>
        <el-button @click="resetForm(formRef)">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { reactive, ref } from "vue";
import http from "@/api";
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { ElMessage, type FormInstance } from "element-plus";
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { findLabels, JdCategory, findAttrsByCategoryIdUnlimitCate } from "@/views/product/interface/category";
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { handleProductIn } from "@/views/product/interface/source";
import Desc from "@/views/product/components/Desc.vue";
const formRef = ref<FormInstance>();
const dialogVisible = ref(false);
const dialogProps = ref<{ [key: string]: any }>({});
const acceptParams = async (row: { [key: string]: any }) => {
  dialogProps.value = row;
  dialogVisible.value = true;
};

defineExpose({ acceptParams });

const resetParams = () => {
  // 清空dialogProps.value.form.maxPrice
  // dialogProps.value.form.maxPrice = {};
  // dialogProps.value.form.productProps = {};
  // dialogProps.value.form.skuProps = {};
  // dialogProps.value.form.props = {
  //   productProps: {},
  //   skuProps: {}
  // };
  // dialogProps.value.rules.productProps = {};
  // dialogProps.value.rules.skuProps = {};
  // categoryPrice.splice(0, categoryPrice.length);
};

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
  dialogVisible.value = false;
};

// 获取多选数量
const getValCount = (val: []) => {
  let count = 0;
  val.forEach((item: any) => {
    if (item.key === "valCount") {
      count = Number(item.fvalue);
    }
  });
  return count; // 返回最终解析到的数量
};

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate((valid, fields) => {
    if (valid) {
      const params: { [key: string]: any } = {
        pid: dialogProps.value.row.pid,
        id: dialogProps.value.row.id,
        category: dialogProps.value.form.category,
        path: dialogProps.value.form.path,
        props: dialogProps.value.form.props
      };
      handleProductIn(params).then(() => {
        ElMessage.success("入库成功");
        dialogVisible.value = false;
        dialogProps.value.getTableList();
      });
    } else {
      console.log("error submit!", fields);
    }
  });
};
// 手动选择
const handleChoseCategory = (value: any) => {
  dialogProps.value.form.category = [11729, 11731, ...value];
  dialogProps.value.form.path = ["鞋靴", "时尚女鞋", ...findLabels(JdCategory, dialogProps.value.form.category)];
  // resetParams();
  http
    .post("/product/category/findAttrsByCategoryIdUnlimitCate", {
      category: dialogProps.value.form.category
    })
    .then(res => {
      dialogProps.value.form.propsOptions = res.data;
      dialogProps.value.form.propsOptions.forEach((item: any) => {
        dialogProps.value.rules.props[`${String(item.id)}`] = [
          { required: true, message: "请选择 " + item.name, trigger: "change" }
        ];
      });
    });
};
</script>
<style scoped>
.el-select {
  width: 50%;
}
.el-radio {
  width: 100%;
  margin-bottom: 10px;
}
.props :deep() .el-radio__label {
  width: 100% !important;
}
.props-category :deep() .el-cascader {
  width: 60%;
}
.dcalc {
  width: calc(100% - 750px);
}
.fm {
  margin: 30px 0;
  :deep() .el-cascader,
  .el-select {
    width: 60% !important;
  }
}
.in :deep() .el-input {
  height: 32px !important;
  line-height: 32px !important;
}
</style>
