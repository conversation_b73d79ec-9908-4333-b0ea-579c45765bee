<template>
  <el-dialog
    v-model="dialogVisible"
    title="修改密码"
    class="card"
    width="30%"
    destroy-on-close
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :draggable="true"
    :align-center="true"
    :z-index="100"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px" label-suffix=" :" style="margin: 30px 0">
      <el-form-item label="旧密码" prop="oldPassword">
        <el-input v-model="form.oldPassword" placeholder="请输入用户名"></el-input>
      </el-form-item>
      <el-form-item label="新密码" prop="newPassword">
        <el-input v-model="form.newPassword" type="password" :show-password="true" placeholder="请输入密码"></el-input>
      </el-form-item>
      <el-form-item label="确认密码" prop="confirmPassword">
        <el-input v-model="form.confirmPassword" type="password" :show-password="true" placeholder="请输入密码"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submit(formRef)">确认修改</el-button>
        <el-button @click="reset(formRef)">重置</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import type { FormRules, FormInstance } from "element-plus";
import { checkPassword } from "@/utils/eleValidate";
// import { changePassword } from "@/api/interface/user";
// import { ElMessage } from "element-plus";
// import { useUserStore } from "@/stores/modules/user";
// import router from "@/routers";
// import { LOGIN_URL } from "@/config";
const dialogVisible = ref(false);
// const userStore = useUserStore();

const openDialog = () => {
  dialogVisible.value = true;
};

const formRef = ref<FormInstance>();

interface From {
  oldPassword: string;
  newPassword: string;
  confirmPassword: string;
}
const form = reactive<From>({
  oldPassword: "",
  newPassword: "",
  confirmPassword: ""
});

const rules = reactive<FormRules>({
  oldPassword: [
    { required: true, message: "请输入旧密码", trigger: "blur" },
    { validator: checkPassword, trigger: "blur" }
  ],
  newPassword: [
    { required: true, message: "请输入密码", trigger: "blur" },
    { validator: checkPassword, trigger: "blur" }
  ],
  confirmPassword: [
    { required: true, message: "请再次输入密码", trigger: "blur" },
    { validator: checkPassword, trigger: "blur" },
    {
      validator: (rule: any, value: string, callback: any) => {
        if (value !== form.newPassword) {
          callback(new Error("两次输入密码不一致"));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ]
});

const reset = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
};

const submit = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;

  await formEl.validate(valid => {
    if (valid) {
      // changePassword({ oldPassword: form.oldPassword, newPassword: form.newPassword }).then(() => {
      //   dialogVisible.value = false;
      //   reset(formEl);
      //   ElMessage({
      //     type: "success",
      //     message: "修改成功，请重新登录！"
      //   });
      //   // userStore.setToken("");
      //   router.replace(LOGIN_URL);
      // });
    }
  });
};

defineExpose({ openDialog });
</script>
<style scoped>
.el-input {
  width: 70%;
}
</style>
