<template>
  <div class="tool-bar-ri">
    <div class="header-icon">
      <AssemblySize id="assemblySize" />
      <!-- <Language id="language" /> -->
      <!-- <SearchMenu id="searchMenu" /> -->
      <!-- <ThemeSetting id="themeSetting" /> -->
      <!-- <Message id="message" /> -->
      <!-- <Fullscreen id="fullscreen" /> -->
      <span></span>
    </div>
    <Avatar />
  </div>
</template>

<script setup lang="ts">
// import { computed, reactive } from "vue";
// import { useUserStore } from "@/stores/modules/user";
import AssemblySize from "./components/AssemblySize.vue";
// import Language from "./components/Language.vue";
// import SearchMenu from "./components/SearchMenu.vue";
// import ThemeSetting from "./components/ThemeSetting.vue";
// import Message from "./components/Message.vue";
// import Fullscreen from "./components/Fullscreen.vue";
import Avatar from "./components/Avatar.vue";
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { User } from "@/api/interface/user";

// const userStore = useUserStore();
// const username = computed(() => userStore.userInfo.username);
// const userDetail = reactive<User.AccountType>({
//   isVip: false,
//   endTime: "",
//   userType: 1
// });
// getUserTypes().then(res => {
//   userDetail.userType = res.data.userType;
//   userDetail.isVip = res.data.isVip;
//   userDetail.endTime = res.data.endTime;
// });
</script>

<style scoped lang="scss">
.tool-bar-ri {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-right: 25px;
  .header-icon {
    display: flex;
    align-items: center;
    & > * {
      margin-left: 21px;
      color: var(--el-header-text-color);
    }
  }
  .username {
    margin: 0 5px;
    font-size: 13px;
    color: var(--el-header-text-color);
  }
}
.tags {
  padding: 0 2px;
}
</style>
