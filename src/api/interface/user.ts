import http from "@/api";
import authMenuList from "@/assets/json/authMenuList.json";
import authButtonList from "@/assets/json/authButtonList.json";

export namespace User {
  export interface AccountType {
    userType: number;
    endTime?: string;
    isVip?: boolean;
  }
}
// 登录模块
export namespace Login {
  export interface ReqLoginForm {
    username: string;
    password: string;
    // rememberMe: boolean;
  }
  export interface ResLogin {
    token: string;
  }
  export interface ResAuthButtons {
    [key: string]: string[];
  }
}

// 用户登录
export const loginApi = (params: Login.ReqLoginForm) => {
  return http.post<Login.ResLogin>(`/auth/login`, params, { loading: false }); // 正常 post json 请求  ==>  application/json
};

// 用户退出登录
export const logoutApi = () => {
  return http.post(`/auth/logout`);
};

// 获取菜单列表
export const getAuthMenuListApi = () => {
  return authMenuList;
};

// 获取按钮权限
export const getAuthButtonListApi = () => {
  return authButtonList;
};

// 注册账号
// export const registerApi = (params: any) => {
//   return http.post(`/user/register`, params, { loading: false });
// };

// 修改密码
// export const changePassword = (data: { oldPassword: string; newPassword: string }) => {
//   return http.post("/user/password", data);
// };
// 获取用户类型
// export const getUserTypes = () => {
//   return http.get<User.AccountType>("/user");
// };
