import http from "@/api";
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { Result, ResPage, ResultData } from "@/api/interface/index";

// 设备模块
export namespace Device {
  export interface DeviceList {
    groupId: number;
    uuid: string;
    serialNumber: string;
    name: string;
    firmware: string;
    space: string;
    model: string;
    createTime: string;
  }
  export interface DeviceAuthList {
    id: string;
    uuid: string;
    name: string;
    scriptId: string;
    scriptName: string;
    expireTime: string;
    trail: string;
    newuuid: string;
    card: string;
    cardType: string;
    channel: string;
    remark: string;
  }
  export interface Group {
    id: number;
    name: string;
  }
  export interface ScriptList {
    id: string;
    name: string;
  }
  export interface CreateGroup {
    name: string;
    id: number;
  }
}

// ===============================================设备分组===============================================
export const doGroupApi = (action: string, params?: any) => {
  switch (action) {
    case "list":
      return http.get<Device.Group>(`/device/group`);
    case "create":
      return http.post<Device.CreateGroup>(`/device/group`, params);
    case "delete":
      return http.delete<Result>(`/device/group`, params);
    case "update":
      return http.put<Result>(`/device/group`, params);
  }
};
// ===============================================设备列表===============================================

export const getDeviceList = (params: Device.DeviceList) => {
  return http.get<ResPage<Device.DeviceList>>(`/device/detail`, params);
};

export const doDeviceApi = (action: string, params?: any) => {
  switch (action) {
    case "list":
      return http.get<ResPage<Device.DeviceList>>(`/device/detail`, params);
    case "delete":
      return http.delete<Result>(`/device/detail`, params);
    case "update":
      return http.put<Result>(`/device/detail`, params);
    case "assign":
      return http.post<Result>(`/device/detail`, params);
    case "create":
      // 绑定设备
      return http.post<Result>(`/device/detail`, params);
  }
};
// 设备筛选列表
export const getDeviceFilterList = () => {
  return http.get<ResultData<Device.DeviceList[]>>("/device/detail/tree");
};
// ===============================================设备授权===============================================
// 授权管理列表
export const getDeviceAuthList = (params: Device.DeviceAuthList) => {
  return http.get<ResPage<Device.DeviceAuthList>>(`/device/auth`, params);
};

// 激活
export const activeDevice = (params: { uuid: string; scriptId: string; card: string }) => {
  return http.post<Result>(`/device/auth`, params);
};
// 换绑
export const activeChange = (params: any) => {
  return http.put<Result>(`/device/auth`, params);
};
export const getDeviceAuthDetail = (params: { id: string }) => {
  return http.get<Result>(`/device/auth/detail`, params);
};

// 根据脚本id获取激活价格
export const getAuthPriceByScriptId = (params: { scriptId: string }) => {
  return http.get(`/device/auth/price`, params);
};
