import http from "@/api";
import { ResPage } from "@/api/interface/index";
import { Products } from "@/api/interface/product";

export namespace Pay {
  export interface Channel {
    id: [number];
  }
  export interface Order {
    url: string;
    orderId: number;
  }
  export interface Card {
    card: [string];
    count: number;
    type: string;
    status: boolean;
    endTime?: string;
    msg: string;
  }
}

// export const getPayChannel = () => {
//   return http.get(`/pay/channel`);
//   return http.get<Pay.Channel>(`/pay/channel`);
// };
export const getScriptList = () => {
  return http.get<ResPage<Products.List>>(`/pay/product`);
};
export const createOrder = (params: {
  action: number;
  scriptId: string;
  cardType: number;
  channel: string;
  // remark: string;
  deviceId?: string;
  count?: number;
}) => {
  /**
   * 直接激活选项: deviceId
   * 购买激活码选项: count
   */
  return http.post<Pay.Order>(`/pay/order`, params);
};

export const getPayOrder = (params: { orderId: number }) => {
  return http.get<Pay.Card>(`/pay/state`, params, { loading: false });
};

export const cancelPay = (params: { orderId: number }) => {
  return http.post<Pay.Card>(`/pay/cancel`, params, { loading: false });
};

export const queryRecord = (params: { traceId: string }) => {
  return http.get(`/pay/record`, params);
};

// ----------------------------------------------------------------
export const createPayOrder = (params: { scriptId: string; cardType: string; channel: string; count: number }) => {
  return http.post<Pay.Order>(`/pay`, params);
};
