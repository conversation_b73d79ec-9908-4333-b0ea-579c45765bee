import http from "@/api";
import { Result, ResPage } from "@/api/interface/index";
import { ElMessage } from "element-plus";

export namespace Profile {
  export interface List {
    id: number;
    content: string;
    createTime: string;
    type: number;
    group: number;
    sku: string;
    keyword: string;
    belong: Belong;
  }
  export interface Group {
    id: number;
    name: string;
  }
  export interface Type {
    id: number;
    name: string;
  }
  export interface Belong {
    username: string;
    nickname: string;
    position: string;
  }
}

// 获取资料类型
export const getProfileType = () => {
  return http.get<ResPage<Profile.Type>>(`/profile/type`);
};

export const getProfileList = (params: any) => {
  return http.get<ResPage<Profile.List>>(`/profile`, params);
};

export const doProfileApi = (action: string, params: any) => {
  switch (action) {
    case "list":
      return http.get<ResPage<Profile.List>>(`/profile`, params);
    case "create":
      return http.post<Result>(`/profile`, params);
    case "delete":
      return http.delete<Result>(`/profile`, params);
  }
};

// 批量上传资料
export const batchProfile = (params: FormData) => {
  return http.post<Result>(`/profile/batch`, params).then(res => {
    ElMessage.success(res.msg);
  });
};
